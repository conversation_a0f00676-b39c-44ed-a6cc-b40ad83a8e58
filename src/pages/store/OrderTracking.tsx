import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiService } from '@/services/api'
import {
  FiPackage, FiTruck, FiCheck, FiSearch, FiAlertCircle,
  FiX, FiClock, FiArrowRight, FiFilter, FiEye, FiTrash2,
  FiShoppingBag, FiInfo, FiChevronDown, FiChevronUp,
  FiDollarSign, FiCalendar, FiMapPin
} from 'react-icons/fi'
import { OrderTracker } from '@/components/store/OrderTracker'
import { toast } from 'react-toastify'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from 'react-i18next'

type Order = {
  id: string
  userId: string
  orderDate: string
  status: string
  storeName?: string
  totalPrice: number
  items: OrderItem[]
  estimatedDelivery?: string
}

type OrderItem = {
  quantity: number
  designJson: string
}

const OrderTracking = () => {
  const { t } = useTranslation('orderTracking')
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const queryClient = useQueryClient()

  // Fetch orders data
  const { data: orders, isLoading, isError } = useQuery<Order[]>({
    queryKey: ['orders'],
    queryFn: async () => {
      return apiService.get<Order[]>('/orders')
    }
  })

  // Cancel order mutation
  const cancelOrderMutation = useMutation({
    mutationFn: async (orderId: string) => {
      return apiService.put(`/orders/${orderId}/cancel`)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      toast.success(t('orderCancelledSuccessfully'))
    },
    onError: (error) => {
      console.error('Error cancelling order:', error)
      toast.error(t('failedToCancelOrder'))
    }
  })

  const canCancelOrder = (order: Order) => {
    const orderDate = new Date(order.orderDate)
    const now = new Date()
    const hoursSinceOrder = (now.getTime() - orderDate.getTime()) / (1000 * 60 * 60)

    return (order.status.toLowerCase() === 'pending' || order.status.toLowerCase() === 'approved') &&
      hoursSinceOrder <= 24 // Can cancel within 24 hours
  }

  const handleCancelOrder = (orderId: string) => {
    if (window.confirm(t('confirmCancelOrder'))) {
      cancelOrderMutation.mutate(orderId)
    }
  }

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-lg'
      case 'approved':
        return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white shadow-lg'
      case 'in production':
        return 'bg-gradient-to-r from-purple-400 to-purple-600 text-white shadow-lg'
      case 'completed':
        return 'bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg'
      case 'cancelled':
        return 'bg-gradient-to-r from-red-400 to-red-600 text-white shadow-lg'
      default:
        return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white shadow-lg'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <FiClock className="w-5 h-5" />
      case 'approved':
        return <FiCheck className="w-5 h-5" />
      case 'in production':
        return <FiTruck className="w-5 h-5" />
      case 'completed':
        return <FiCheck className="w-5 h-5" />
      case 'cancelled':
        return <FiX className="w-5 h-5" />
      default:
        return <FiPackage className="w-5 h-5" />
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount)
  }

  const toggleOrderExpansion = (orderId: string) => {
    if (expandedOrderId === orderId) {
      setExpandedOrderId(null)
    } else {
      setExpandedOrderId(orderId)
    }
  }

  const filterOrders = (orders: Order[] = []) => {
    let filtered = orders

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.status.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order =>
        order.status.toLowerCase() === statusFilter.toLowerCase()
      )
    }

    // Sort orders
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime()
        case 'oldest':
          return new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()
        case 'price-high':
          return b.totalPrice - a.totalPrice
        case 'price-low':
          return a.totalPrice - b.totalPrice
        default:
          return 0
      }
    })

    return filtered
  }

  const parseDesignJson = (designJson: string) => {
    try {
      return JSON.parse(designJson)
    } catch (e) {
      console.error('Failed to parse design JSON:', e)
      return null
    }
  }

  const getOrderSummary = (order: Order) => {
    const totalItems = order.items.reduce((sum, item) => sum + item.quantity, 0)
    const uniqueDesigns = order.items.length
    return { totalItems, uniqueDesigns }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
        <div className="container mx-auto p-6 max-w-7xl">
          <div className="flex justify-center items-center h-64">
            <div className="relative">
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-200"></div>
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
            </div>
            <p className="ml-4 text-lg text-gray-600">{t('loading', { defaultValue: 'Loading your orders...' })}</p>
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
        <div className="container mx-auto p-6 max-w-7xl">
          <div className="flex flex-col justify-center items-center h-64">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-4 text-red-500"
            >
              <FiAlertCircle size={80} />
            </motion.div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">{t('errorLoadingOrders')}</h2>
            <p className="text-gray-600 text-center max-w-md text-lg">
              {t('errorLoadingOrdersDescription')}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-6 px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 font-semibold"
            >
              {t('tryAgain')}
            </button>
          </div>
        </div>
      </div>
    )
  }

  const filteredOrders = filterOrders(orders)
  const statusOptions = [
    { value: 'all', label: t('allOrders'), count: orders?.length || 0 },
    { value: 'pending', label: t('orderStatus.pending'), count: orders?.filter(o => o.status.toLowerCase() === 'pending').length || 0 },
    { value: 'approved', label: t('orderStatus.approved'), count: orders?.filter(o => o.status.toLowerCase() === 'approved').length || 0 },
    { value: 'in production', label: t('orderStatus.in_production'), count: orders?.filter(o => o.status.toLowerCase() === 'in production').length || 0 },
    { value: 'completed', label: t('orderStatus.completed'), count: orders?.filter(o => o.status.toLowerCase() === 'completed').length || 0 },
    { value: 'cancelled', label: t('orderStatus.cancelled'), count: orders?.filter(o => o.status.toLowerCase() === 'cancelled').length || 0 }
  ]

  const totalRevenue = orders?.reduce((sum, order) => sum + order.totalPrice, 0) || 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-purple-200 rounded-full opacity-10 blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-pink-200 rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto p-6 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8 text-center"
        >
          <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
            {t('yourOrders')}
          </h1>
          <p className="text-xl text-gray-600">{t('trackAndManageOrders')}</p>
        </motion.div>
        
        {/* Total Revenue Card - Moved to top for better visibility */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl mb-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                <FiDollarSign className="w-10 h-10" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">{t('totalRevenue', { defaultValue: 'Total Revenue' })}</h2>
                <p className="text-purple-100">{t('totalRevenueDescription', { defaultValue: 'Your total earnings from all orders' })}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-4xl font-bold">{formatCurrency(totalRevenue)}</p>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"
        >

          {/* Status Cards */}
          {statusOptions.slice(1).map((status, index) => (
            <motion.div
              key={status.value}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.05 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{status.label}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-1">{status.count}</p>
                </div>
                <div className={`p-3 rounded-xl ${getStatusClass(status.value)} opacity-90`}>
                  {getStatusIcon(status.value)}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8"
        >
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400 w-5 h-5" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-3 border-2 border-gray-200 rounded-xl leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:bg-white focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                placeholder={t('searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="appearance-none bg-gray-50 border-2 border-gray-200 rounded-xl px-4 py-3 pr-10 focus:outline-none focus:bg-white focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all font-medium"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label} ({option.count})
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                <FiFilter className="text-gray-400 w-5 h-5" />
              </div>
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none bg-gray-50 border-2 border-gray-200 rounded-xl px-4 py-3 pr-10 focus:outline-none focus:bg-white focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all font-medium"
              >
                <option value="newest">{t('newestFirst')}</option>
                <option value="oldest">{t('oldestFirst')}</option>
                <option value="price-high">{t('priceHighToLow')}</option>
                <option value="price-low">{t('priceLowToHigh')}</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                <FiArrowRight className="text-gray-400 w-5 h-5 rotate-90" />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Orders List */}
        <AnimatePresence>
          {filteredOrders && filteredOrders.length > 0 ? (
            <div className="space-y-6">
              {filteredOrders.map((order, index) => {
                const summary = getOrderSummary(order)
                return (
                  <motion.div
                    key={order.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
                  >
                    <div
                      className="p-6 cursor-pointer bg-gradient-to-r from-gray-50 to-white"
                      onClick={() => toggleOrderExpansion(order.id)}
                    >
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <div className="p-2 bg-purple-100 rounded-lg">
                              <FiPackage className="w-6 h-6 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900">
                              {t('orderNumber', { id: order.id.substring(0, 8).toUpperCase() })}
                            </h3>
                            <span className={`px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2 ${getStatusClass(order.status)}`}>
                              {getStatusIcon(order.status)}
                              {t(`orderStatus.${order.status.toLowerCase().replace(' ', '_')}`)}
                            </span>
                            {canCancelOrder(order) && (
                              <span className="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full font-medium animate-pulse">
                                {t('canCancel')}
                              </span>
                            )}
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                            <div className="flex items-center gap-2 text-gray-600">
                              <FiCalendar className="w-4 h-4 text-gray-400" />
                              <span>{formatDate(order.orderDate)}</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-600">
                              <FiShoppingBag className="w-4 h-4 text-gray-400" />
                              <span>{t('itemsCount', { totalItems: summary.totalItems, uniqueDesigns: summary.uniqueDesigns })}</span>
                            </div>
                            {order.storeName && (
                              <div className="flex items-center gap-2 text-gray-600">
                                <FiMapPin className="w-4 h-4 text-gray-400" />
                                <span>{order.storeName}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-6">
                          <div className="text-right">
                            <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                              {formatCurrency(order.totalPrice)}
                            </p>
                            {order.estimatedDelivery && (
                              <p className="text-sm text-gray-500 mt-1">{t('estimatedDelivery', { date: formatDate(order.estimatedDelivery) })}</p>
                            )}
                          </div>

                          <div className="flex items-center gap-3">
                            {/* Prominent Cancel Button specifically for Pending Orders */}
                            {order.status.toLowerCase() === 'pending' && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleCancelOrder(order.id)
                                }}
                                disabled={cancelOrderMutation.isPending && cancelOrderMutation.variables === order.id}
                                className="flex items-center gap-2 py-2 px-4 bg-red-600 text-white hover:bg-red-700 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-md"
                              >
                                {cancelOrderMutation.isPending && cancelOrderMutation.variables === order.id ? (
                                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-current border-t-transparent"></div>
                                ) : (
                                  <FiX className="w-5 h-5" />
                                )}
                                <span className="font-medium">{t('cancel')}</span>
                              </button>
                            )}
                            
                            {/* Regular Cancel Button for Other Cancellable Orders */}
                            {canCancelOrder(order) && order.status.toLowerCase() !== 'pending' && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleCancelOrder(order.id)
                                }}
                                disabled={cancelOrderMutation.isPending && cancelOrderMutation.variables === order.id}
                                className="p-3 bg-red-100 text-red-600 hover:bg-red-600 hover:text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-110"
                                title={t('cancel')}
                              >
                                {cancelOrderMutation.isPending && cancelOrderMutation.variables === order.id ? (
                                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-current border-t-transparent"></div>
                                ) : (
                                  <FiTrash2 className="w-5 h-5" />
                                )}
                              </button>
                            )}
                            
                            {/* Toggle Details Button */}
                            <button 
                              className="p-3 bg-purple-100 text-purple-600 hover:bg-purple-600 hover:text-white rounded-xl transition-all duration-300 transform hover:scale-110"
                              onClick={() => toggleOrderExpansion(order.id)}
                            >
                              {expandedOrderId === order.id ? (
                                <FiChevronUp className="w-5 h-5" />
                              ) : (
                                <FiChevronDown className="w-5 h-5" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <AnimatePresence>
                      {expandedOrderId === order.id && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="border-t-2 border-gray-100 bg-gradient-to-b from-gray-50 to-white"
                        >
                          <div className="p-6">
                            {/* Order Tracker */}
                            <div className="mb-8">
                              <h4 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                                <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
                                {t('orderProgress')}
                              </h4>
                              <OrderTracker order={order} />
                            </div>

                            {/* Order Items */}
                            <div>
                              <h4 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                                <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
                                {t('orderItems')}
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {order.items.map((item, index) => {
                                  const design = parseDesignJson(item.designJson)
                                  if (!design) return null

                                  return (
                                    <div key={index} className="bg-white rounded-xl p-6 border-2 border-gray-200 hover:border-purple-300 transition-all duration-300">
                                      <div className="flex items-start gap-4">
                                        <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-xl flex items-center justify-center flex-shrink-0">
                                          <FiPackage className="w-8 h-8 text-white" />
                                        </div>
                                        <div className="flex-1">
                                          <h5 className="font-bold text-lg text-gray-900 mb-1">
                                            {design.model || design.name || t('customFurnitureDesign')}
                                          </h5>
                                          <p className="text-sm text-gray-600 mb-3">
                                            {design.armchairType || design.type || t('armchair')} • {t('quantity')}: {item.quantity}
                                          </p>

                                          {/* Design details */}
                                          <div className="space-y-2">
                                            {design.barcode && (
                                              <div className="flex items-center gap-2 text-sm">
                                                <span className="text-gray-500">{t('barcode')}:</span>
                                                <span className="font-mono font-medium">{design.barcode}</span>
                                              </div>
                                            )}
                                            <div className="flex flex-wrap gap-2 mt-2">
                                              {design.sharedFabric && (
                                                <span className="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium">
                                                  {t('fabric')}: {design.sharedFabric.option}
                                                </span>
                                              )}
                                              {design.armrest && (
                                                <span className="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full font-medium">
                                                  {t('arm')}: {design.armrest.main}
                                                </span>
                                              )}
                                              {design.legs && (
                                                <span className="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                                                  {t('leg')}: {design.legs}
                                                </span>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          <p className="text-xl font-bold text-gray-900">
                                            {formatCurrency((design.totalPrice || 0) * item.quantity)}
                                          </p>
                                          <p className="text-sm text-gray-500">
                                            {formatCurrency(design.totalPrice || 0)} {t('each')}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                )
              })}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-16 text-center"
            >
              <div className="flex flex-col items-center justify-center">
                <div className="mb-8 relative">
                  <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <FiPackage className="w-16 h-16 text-gray-400" />
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    <FiX className="w-6 h-6 text-white" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {searchTerm || statusFilter !== 'all' ? t('noMatchingOrdersFound') : t('noOrdersYet')}
                </h3>
                <p className="text-gray-600 mb-8 max-w-md text-lg">
                  {searchTerm || statusFilter !== 'all'
                    ? t('tryAdjustingFilters')
                    : t('startShoppingDescription')
                  }
                </p>
                {(!searchTerm && statusFilter === 'all') && (
                  <button
                    onClick={() => window.location.href = '/store/design'}
                    className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold text-lg rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-xl"
                  >
                    {t('startShopping')}
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default OrderTracking