import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { BsFullscreen } from 'react-icons/bs';
import BabylonScene from '@/components/store/BabylonScene';
import {
  backendService,
  apiService,
  PricingDataDto,
  PartPrice,
} from '@/services/api';
import { useCart } from '@/hooks/useCart';
import { toast } from 'react-toastify';

// Memoized BabylonScene to prevent re-renders when customer info changes
const MemoizedBabylonScene = memo(BabylonScene, (prevProps, nextProps) => {
  // Custom comparison function - only re-render if design actually changes
  return (
    JSON.stringify(prevProps.design) === JSON.stringify(nextProps.design) &&
    prevProps.furnitureType === nextProps.furnitureType &&
    prevProps.backCushion === nextProps.backCushion &&
    prevProps.getPartFilePath === nextProps.getPartFilePath &&
    JSON.stringify(prevProps.decorativeCushions) === JSON.stringify(nextProps.decorativeCushions)
  );
});

// BabylonScene compatible design type
interface BabylonDesign {
  furnitureType: 'armchair' | 'bergere';
  frameColor: string;
  upholstery: string;
  legType: string;
  cushionSize: string;
  cushionFabric: string;
  backCushion: string;
  decorativeCushions: {
    size: string;
    fabric: string;
    quantity: number;
  };
  addOns: string[];

  // Additional properties used in BabylonScene
  lowerFrame: string;
  sharedFabric: string;
  legs: string;
  legFabric: string;
  woodVeneer: string;
  woodVeneerFabric: string;
  armrest: {
    main: string;
    sub: string[];
  };
  cushion: {
    option: string;
  };
  // Seat options
  seat: {
    type: string;
    color: string;
    options: {
      cektirme: boolean;
      tekParca: boolean;
      bombe: boolean;
      kirisiklik: boolean;
      kulak: boolean;
    };
  };
  // Fabric options
  fabric: {
    type: string; // e.g., 'brown', 'grey', 'cartela1'
    color: string; // e.g., '1', '2', '3', etc.
  };
  // Skeleton options for bergere
  skeleton?: {
    type: string;
    color: string;
  };
}

interface CustomerInfo {
  firstName: string;
  lastName: string;
}

interface Design {
  furnitureType: string;
  fabric: {
    type: string;
    color: string;
  };
  frame: {
    type: string;
    color: string;
  };
  arm: {
    type: string;
    color: string;
    hasPapel: boolean;
    hasFrontFlap: boolean;
    hasKulak: boolean;
    hasDugme?: boolean;
    hasBoğumKapiton?: boolean;
  };
  seat: {
    type: string;
    color: string;
    options: {
      cektirme: boolean;
      tekParca: boolean;
      bombe: boolean;
      kirisiklik: boolean;
      kulak: boolean;
    };
  };
  leg: {
    type: string;
    color: string;
  };
  skeleton?: {
    type: string;
    color: string;
  };
}

interface Pillows {
  backPillow: {
    type: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow1: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow2: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow3: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
}

interface PriceBreakdown {
  framePrice: number;
  fabricPrice: number;
  legPrice: number;
  cushionPrice: number;
  additionalOptionsPrice: number;
  skeletonPrice: number;
  totalPrice: number;
}

interface AllPartPrices {
  frames: Record<string, number>;
  fabrics: Record<string, number>;
  legs: Record<string, number>;
  seats: Record<string, number>;
  arms: Record<string, number>;
  armOptions: Record<string, number>;
  woodColors: Record<string, number>;
  pillows: Record<string, number>;
}

const FurnitureCustomizer: React.FC = () => {
  // State and refs remain largely the same
  const navigate = useNavigate();
  const location = useLocation();
  const { fetchCart } = useCart();

  const [isModelLoading, setIsModelLoading] = useState<boolean>(true);

  // Rest of the state definitions...
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
  });
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    framePrice: 0,
    fabricPrice: 0,
    legPrice: 0,
    cushionPrice: 0,
    additionalOptionsPrice: 0,
    skeletonPrice: 0,
    totalPrice: 0,
  });
  const [isLoadingPrice, setIsLoadingPrice] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [barcodeText, setBarcodeText] = useState<string>(
    'K1(1)_1-1-1(1)-1-1',
  );

  // Add state to track if saved design has been loaded
  const [savedDesignLoaded, setSavedDesignLoaded] = useState<boolean>(false);

  // Reset savedDesignLoaded when location.state changes or component mounts without saved design
  useEffect(() => {
    if (!location.state?.savedDesign) {
      setSavedDesignLoaded(false);
    }
  }, [location.state]);

  // State for BabylonScene configuration
  const [babylonDesign, setBabylonDesign] = useState<BabylonDesign>({
    furnitureType: 'armchair',
    frameColor: 'CevizAhsap',
    upholstery: 'brown',
    legType: '1 Ahşap',
    cushionSize: '50*50',
    cushionFabric: 'brown',
    backCushion: 'Silikon Elyaf',
    decorativeCushions: {
      size: '50*50',
      fabric: 'brown',
      quantity: 1,
    },
    addOns: [],

    // Default values for BabylonScene
    lowerFrame: '1',
    sharedFabric: 'brown',
    legs: '1',
    legFabric: 'CevizAhsap',
    woodVeneer: '1',
    woodVeneerFabric: 'CevizAhsap',
    armrest: {
      main: '1',
      sub: [],
    },
    cushion: {
      option: '1',
    },
    seat: {
      type: '1',
      color: 'brown',
      options: {
        cektirme: false,
        tekParca: false,
        bombe: false,
        kirisiklik: false,
        kulak: false,
      },
    },
    // Initialize fabric options with cartela1 as default
    fabric: {
      type: 'cartela1',
      color: '1',
    },
  });
  const [design, setDesign] = useState<Design>({
    furnitureType: 'armchair',
    fabric: {
      type: 'cartela1',
      color: '1',
    },
    frame: {
      type: '1',
      color: 'CevizAhsap',
    },
    arm: {
      type: 'KOL 1',
      color: 'CevizAhsap',
      hasPapel: false,
      hasFrontFlap: false,
      hasKulak: false,
      hasDugme: false,
      hasBoğumKapiton: false,
    },
    seat: {
      type: '1',
      color: 'brown',
      options: {
        cektirme: false,
        tekParca: false,
        bombe: false,
        kirisiklik: false,
        kulak: false,
      },
    },
    leg: {
      type: '1 Ahşap',
      color: 'CevizAhsap',
    },
    skeleton: {
      type: '1',
      color: 'CevizAhsap',
    },
  });

  // Separate state for pillows to prevent unnecessary 3D re-renders
  const [pillows, setPillows] = useState<Pillows>({
    backPillow: {
      type: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 0
    },
    pillow1: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 0
    },
    pillow2: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 0
    },
    pillow3: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 0
    },
  });

  // FIXED: Improved getPartFilePath function for correct file path generation
  const getPartFilePath = useCallback((type: string, subType: string, name: string): string => {
    if (!name || name === '') return '';

    console.log(`Getting path for ${subType}: ${name}`);

    // Map to the correct file paths in your assets structure
    switch (subType) {
      case 'lowerFrame':
        return `/assets/${type}/objects/AltKasa/${name}`;

      case 'legs':
        // The leg files have names like "1 Ahşap.glb", "3 Metal.glb"
        // If name is just a number (e.g., "1"), append the appropriate type
        if (/^\d+$/.test(name)) {
          const legFullName = `${name} Ahşap`; // Default to Ahşap (wooden) if only number is provided
          return `/assets/${type}/objects/Ayaklar/${legFullName}`;
        }
        // Otherwise use the full name as provided
        return `/assets/${type}/objects/Ayaklar/${name}`;

      case 'woodVeneer':
        return `/assets/${type}/objects/AhsapKasa/${name}`;

      case 'armrest':
        // Handle different arm naming conventions based on file structure
        // Use file selection based on if this is the BOŞ or Kulak (4) option

        // First check if this is a sub-option case like "KOL 1:3-5" (for Papel, Kulak, etc.)
        if (name.includes(':')) {
          const [armBase, option] = name.split(':');

          // Handle armrest sub-options
          if (/^\d+(-\d+)*$/.test(option)) {
            // This is a numeric option or combination (3, 4, 5, 3-5, etc.)
            console.log(`Loading armrest sub-option: ${armBase}/${option}`);
            return `/assets/${type}/objects/Kol/${armBase}/${option}`;
          }

          // Check if we need to load the Kulak model (4) or the default BOŞ model
          const modelToLoad = option === '4' ? '4' : 'BOŞ';
          console.log(`Loading main armrest: ${armBase}/${modelToLoad}`);
          return `/assets/${type}/objects/Kol/${armBase}/${modelToLoad}`;
        }

        // Handle basic numbered armrests without options
        if (/^\d+$/.test(name)) {
          // If only a number is provided, use KOL format
          return `/assets/${type}/objects/Kol/KOL ${name}/BOŞ`;
        } else if (name.startsWith('KOL')) {
          // Extract the number part from "KOL X"
          const armNumber = name.split(' ')[1];
          console.log(`Loading armrest: KOL ${armNumber}/BOŞ`);
          return `/assets/${type}/objects/Kol/KOL ${armNumber}/BOŞ`;
        } else {
          // Use as is (fallback)
          console.log(`Loading armrest fallback: ${name}/BOŞ`);
          return `/assets/${type}/objects/Kol/${name}/BOŞ`;
        }

      case 'armrestSub':
        // Special sub components for armrests
        if (name === 'papel') return `/assets/${type}/objects/Kol/papel`;
        if (name === 'onKlapa') return `/assets/${type}/objects/Kol/onKlapa`;
        return '';

      case 'armrestSubOptions':
        // Alias to support BabylonScene use of 'armrestSubOptions'
        // Expected format: "KOL 1:3" where left is arm directory, right is code
        if (name.includes(':')) {
          const [dir, code] = name.split(':');
          return `/assets/${type}/objects/Kol/${dir}/${code}`;
        }
        return '';

      case 'cushion':
        // Based on the file structure, cushions are in Minder/Kulaksız/[type]
        // The default file to load should be BOŞ.glb
        if (/^\d+$/.test(name)) {
          // If just a number is provided, use the BOŞ file for that type
          console.log(`Loading cushion type: ${name}/BOŞ`);
          return `/assets/${type}/objects/Minder/Kulaksız/${name}/BOŞ`;
        } else {
          // Otherwise use the provided name directly
          console.log(`Loading cushion: ${name}`);
          return `/assets/${type}/objects/Minder/Kulaksız/${name}`;
        }

      case 'seat':
        // For bergere, use skeleton/seat structure
        if (type === 'bergere') {
          // Extract just the number if it's in format like "seatFile:1:BOŞ"
          let seatNumber = name;
          if (name.includes(':')) {
            const parts = name.split(':');
            seatNumber = parts[1] || '1';
          }
          return `/assets/${type}/skeleton/${seatNumber}/seat.glb`;
        }
        // For seat models, we use the existing structure in Minder/Kulaksız
        if (name.startsWith('seatFile:')) {
          // Format: "seatFile:seatType:optionList" (e.g., "seatFile:1:1-3-4" or "seatFile:2:")
          const parts = name.split(':');
          const seatType = parts[1]; // 1, 2, or 3
          const optionString = parts[2] || 'BOŞ'; // Use BOŞ as default if no options

          // For now, always use Kulaksız as this is handled by the seat file name
          const seatDir = 'Kulaksız';
          console.log(`Loading seat file: ${seatDir}/${seatType}/${optionString}`);
          return `/assets/${type}/objects/Minder/${seatDir}/${seatType}/${optionString}`;
        }

        // If just a seat type number is provided (1, 2, 3)
        if (/^\d+$/.test(name)) {
          // Default to the empty model for the given seat type
          // For now, always use Kulaksız as this is handled by the seat file name
          const seatDir = 'Kulaksız';
          console.log(`Loading basic seat type: ${seatDir}/${name}/BOŞ`);
          return `/assets/${type}/objects/Minder/${seatDir}/${name}/BOŞ`;
        }

        // Fallback case
        console.warn(`Unhandled seat path request: ${name}`);
        return `/assets/${type}/objects/Minder/Kulaksız/1/BOŞ`; // Fallback to default seat

      case 'skeleton':
        // For bergere skeleton
        if (type === 'bergere') {
          // Extract just the number if it's in format like "seatFile:1:BOŞ"
          let skeletonNumber = name;
          if (name.includes(':')) {
            const parts = name.split(':');
            skeletonNumber = parts[1] || '1';
          }
          return `/assets/${type}/skeleton/${skeletonNumber}/skeleton.glb`;
        }
        return `/assets/${type}/skeleton/${name}/skeleton.glb`;

      default:
        return '';
    }
  }, []); // Remove dependency to prevent re-creation

  // Update BabylonDesign when regular design changes
  const updateBabylonDesign = useCallback(() => {
    // Build armrest sub-option codes - separate base types from additional options
    const subOptions: string[] = [];
    const armDir = design.arm.type; // Use the selected KOL type (KOL 1, KOL 2, etc.)

    // Handle base types (1 and 2) - mutually exclusive
    const baseOptions: string[] = [];
    if (design.arm.hasDugme) baseOptions.push('1'); // 1 = Düğme Kapiton
    if (design.arm.hasBoğumKapiton) baseOptions.push('2'); // 2 = Boğum Kapiton

    // Handle additional options (3, 4, 5) - can be combined
    const additionalOptions: string[] = [];
    if (design.arm.hasPapel) additionalOptions.push('3'); // 3 = Papel
    if (design.arm.hasKulak) additionalOptions.push('4'); // 4 = Kulak
    if (design.arm.hasFrontFlap) additionalOptions.push('5'); // 5 = Ön Klapa

    // Create separate file paths for base type and additional options
    if (baseOptions.length > 0) {
      // Base type file (1 or 2), potentially with Kulak (4)
      if (design.arm.hasKulak) {
        // Base type with Kulak: "1-4" or "2-4"
        subOptions.push(`${armDir}:${baseOptions[0]}-4`);
      } else {
        // Base type alone: "1" or "2"
        subOptions.push(`${armDir}:${baseOptions[0]}`);
      }
    }

    if (additionalOptions.length > 0 && !baseOptions.length) {
      // Only additional options without base type
      const combined = additionalOptions.join('-');
      subOptions.push(`${armDir}:${combined}`);
    } else if (additionalOptions.length > 0 && baseOptions.length > 0) {
      // Additional options separate from base type
      // Filter out Kulak since it's already included with base type
      const filteredAdditional = additionalOptions.filter(opt => opt !== '4');
      if (filteredAdditional.length > 0) {
        const combined = filteredAdditional.join('-');
        subOptions.push(`${armDir}:${combined}`);
      }
    }

    // Build seat file parameter including selected seat sub-options
    const seatOptionCodes: string[] = [];
    if (design.seat.options.cektirme) seatOptionCodes.push('1');
    if (design.seat.options.tekParca) seatOptionCodes.push('3');
    if (design.seat.options.bombe) seatOptionCodes.push('4');
    const seatOptionString = seatOptionCodes.length > 0 ? seatOptionCodes.join('-') : 'BOŞ';
    const seatFileParam = `seatFile:${design.seat.type}:${seatOptionString}`;

    console.log('Updating Babylon Design:', design);

    // Set the corresponding options for BabylonScene
    setBabylonDesign({
      // Basic properties for BabylonScene
      furnitureType: design.furnitureType as 'armchair' | 'bergere',
      frameColor: design.frame.color,
      upholstery: design.fabric.type,
      legType: design.leg.type,
      cushionSize: design.seat.type,
      cushionFabric: design.fabric.type,
      backCushion: pillows.backPillow.type, // Use the actual back pillow type
      decorativeCushions: {
        size: '50*50', // Static value as pillows don't affect 3D model
        fabric: design.fabric.type,
        quantity: 1,
      },
      addOns: [],

      // BabylonScene specific mapping - these properties match the paths in BabylonScene.tsx
      // and are used to load the correct 3D models
      lowerFrame: design.frame.type, // For AltKasa (Lower Frame)
      sharedFabric: design.fabric.type, // Shared fabric for most parts
      legs: design.leg.type, // For Ayaklar (Legs)
      legFabric: design.leg.color, // Leg color/material
      woodVeneer: design.frame.type, // For wood veneer parts
      woodVeneerFabric: design.frame.color, // Wood veneer color/material
      armrest: {
        // Load BOŞ only when option 4 (Kulak) is NOT selected
        main: design.arm.hasKulak ? '' : `${armDir}:BOŞ`,
        sub: subOptions, // Separate armrest component files (1, 2, 1-4, 2-4, 3-4-5, etc.)
      },
      cushion: {
        option: design.seat.type, // For seat cushion type (Kulaksız or Kulaklı)
      },
      // Add the seat property to match the BabylonDesign interface
      seat: {
        type: seatFileParam,
        color: design.seat.color,
        options: design.seat.options,
      },
      // Add the fabric property
      fabric: {
        type: design.fabric.type,
        color: design.fabric.color,
      },
      // Add skeleton property for bergere
      skeleton: design.skeleton,
    });
  }, [
    design.furnitureType,
    design.frame,
    design.fabric,
    design.leg,
    design.arm,
    design.seat,
    design.skeleton,
    pillows.backPillow.type
  ]); // Only depend on fields that affect the 3D model

  // Event handlers remain mostly the same
  const handleFurnitureTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newFurnitureType = e.target.value as 'armchair' | 'bergere';
    setDesign({ ...design, furnitureType: newFurnitureType });
    // Update babylon design immediately with the new furniture type
    setBabylonDesign(prev => ({ ...prev, furnitureType: newFurnitureType }));
  };

  // Other handlers...
  const handleFabricTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, fabric: { ...design.fabric, type: e.target.value } });
  };

  const handleFabricColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, fabric: { ...design.fabric, color: e.target.value } });
  };

  const handleArmTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, arm: { ...design.arm, type: e.target.value } });
    updateBabylonDesign();
  };

  const handleArmOptionChange = (option: keyof Design['arm'], checked: boolean) => {
    let newArmState = { ...design.arm };

    // Handle mutually exclusive options 1 and 2
    if (option === 'hasDugme' && checked) {
      // Clear option 2 when selecting option 1
      newArmState = { ...newArmState, hasBoğumKapiton: false, [option]: checked };
    } else if (option === 'hasBoğumKapiton' && checked) {
      // Clear option 1 when selecting option 2
      newArmState = { ...newArmState, hasDugme: false, [option]: checked };
    } else {
      newArmState[option] = checked;
    }

    // If kulak option is being changed, sync with seat kulak option
    if (option === 'hasKulak') {
      setDesign({
        ...design,
        arm: newArmState,
        seat: {
          ...design.seat,
          options: {
            ...design.seat.options,
            kulak: checked
          }
        }
      });
    } else {
      setDesign({ ...design, arm: newArmState });
    }
    updateBabylonDesign();
  };

  const handleSeatTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, seat: { ...design.seat, type: e.target.value } });
    updateBabylonDesign();
  };

  const handleSeatOptionChange = (option: string, value: string | boolean) => {
    if (option === 'options') {
      // Handle checkbox options
      const optionName = value as string;
      const isChecked = !design.seat.options[optionName as keyof typeof design.seat.options];

      // If kulak option is being changed, sync with arm kulak option
      if (optionName === 'kulak') {
        setDesign({
          ...design,
          seat: {
            ...design.seat,
            options: {
              ...design.seat.options,
              [optionName]: isChecked,
            },
          },
          arm: {
            ...design.arm,
            hasKulak: isChecked
          }
        });
      } else {
        setDesign({
          ...design,
          seat: {
            ...design.seat,
            options: {
              ...design.seat.options,
              [optionName]: isChecked,
            },
          },
        });
      }
    } else {
      // Handle dropdown options
      setDesign({ ...design, seat: { ...design.seat, [option]: value } });
    }
    updateBabylonDesign();
  };

  const handleFrameTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, frame: { ...design.frame, type: e.target.value } });
  };

  const handleFrameColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, frame: { ...design.frame, color: e.target.value } });
  };

  const handleLegTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLegType = e.target.value;
    const isMetalLeg = newLegType.includes('Metal');
    const isWoodenLeg = newLegType.includes('Ahşap');

    // Reset color to appropriate default when switching between wooden and metal
    let newColor = design.leg.color;
    if (isWoodenLeg && ['Bronz', 'Gold', 'Nikel'].includes(design.leg.color)) {
      // If switching to wooden leg but current color is metal, reset to default wooden color
      newColor = 'BeyazAhsap'; // Default to Beyaz instead of Ceviz
    } else if (isMetalLeg && design.leg.color === 'CevizAhsap') {
      // If switching to metal leg and current color is Ceviz, reset to default metal color
      newColor = 'Bronz';
    } else if (isMetalLeg && design.leg.color.includes('Ahsap') && design.leg.color !== 'CevizAhsap') {
      // Keep other wooden colors when switching to metal, only remove Ceviz
      // No change needed for other wooden colors
    }

    setDesign({
      ...design,
      leg: {
        ...design.leg,
        type: newLegType,
        color: newColor
      }
    });
  };

  const handleLegColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, leg: { ...design.leg, color: e.target.value } });
  };

  const handlePillowChange = (pillow: keyof Pillows, field: string, value: string | number) => {
    setPillows(prev => ({
      ...prev,
      [pillow]: {
        ...prev[pillow],
        [field]: value
      }
    }));

    // Recalculate price when pillow configuration changes
    setTimeout(() => {
      if (Object.keys(pricingData).length > 0) {
        calculateTotalPrice(pricingData, design, pillows);
      }
    }, 100); // Small delay to ensure state update
  };

  const handleCustomerInfoChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo({ ...customerInfo, [field]: value });
    // Customer info doesn't affect the 3D model, so no need to update Babylon design
  };

  // Add error handler for 3D rendering

  // Store all part prices
  const [pricingData, setPricingData] = useState<unknown>({});
  const [allPartPrices, setAllPartPrices] = useState<AllPartPrices>({
    frames: {},
    fabrics: {},
    legs: {},
    seats: {},
    arms: {},
    armOptions: {},
    woodColors: {},
    pillows: {},
  });

  // Calculate total price based on selected parts
  // const calculateTotalPrice = useCallback((prices: AllPartPrices = allPartPrices) => {
  //   try {
  //     // Start with base prices
  //     let framePrice = prices.frames[design.frame.type] || 0;
  //     let fabricPrice = prices.fabrics[design.fabric.type] || 0;

  //     // For legs, try both formats (with and without the type suffix)
  //     let legPrice = prices.legs[design.leg.type] ||
  //       prices.legs[design.leg.type.split(' ')[0]] || 0;

  //     let seatPrice = prices.seats[design.seat.type] || 0;

  //     // For arms, try both formats
  //     let armPrice = prices.arms[design.arm.type] ||
  //       prices.arms[design.arm.type.split(' ')[1]] || 0;

  //     let additionalOptionsPrice = 0;
  //     let cushionPrice = 0;

  //     // Add woodColor price
  //     framePrice += prices.woodColors[design.frame.color] || 0;

  //     // Add arm option prices if selected
  //     if (design.arm.hasPapel) {
  //       additionalOptionsPrice += prices.armOptions['hasPapel'] || 0;
  //     }
  //     if (design.arm.hasKulak) {
  //       additionalOptionsPrice += prices.armOptions['hasKulak'] || 0;
  //     }
  //     if (design.arm.hasFrontFlap) {
  //       additionalOptionsPrice += prices.armOptions['hasFrontFlap'] || 0;
  //     }

  //     // Add pillow prices, checking if "Yok" (None) is selected
  //     if (design.pillows.backPillow && design.pillows.backPillow !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.backPillow] || 0;
  //     }
  //     if (design.pillows.pillow1 && design.pillows.pillow1 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow1] || 0;
  //     }
  //     if (design.pillows.pillow2 && design.pillows.pillow2 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow2] || 0;
  //     }
  //     if (design.pillows.pillow3 && design.pillows.pillow3 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow3] || 0;
  //     }

  //     // Calculate total
  //     const totalPrice = framePrice + fabricPrice + legPrice + seatPrice + armPrice + additionalOptionsPrice + cushionPrice;

  //     // Update price breakdown
  //     setPriceBreakdown({
  //       framePrice,
  //       fabricPrice,
  //       legPrice,
  //       cushionPrice,
  //       additionalOptionsPrice,
  //       totalPrice,
  //     });
  //   } catch (error) {
  //     console.error("Error calculating price:", error);
  //     // Set fallback price
  //     setPriceBreakdown((prev) => ({
  //       ...prev,
  //       totalPrice: 40000, // Simple fallback
  //     }));
  //   }
  // }, [design, allPartPrices]);

  // Transform backend pricing DTO or array data into lookup object
  const processPriceData = useCallback((priceData: PricingDataDto | any) => {
    // Convert to lookup object for faster price calculations
    const priceLookup: AllPartPrices = {
      frames: {},
      fabrics: {},
      legs: {},
      seats: {},
      arms: {},
      armOptions: {},
      woodColors: {},
      pillows: {},
    };

    console.log('Processing price data:', priceData);

    // Handle the new API response structure
    if (priceData) {
      // Frames / lower frame base
      if (priceData.lowerFrame) {
        Object.entries(priceData.lowerFrame).forEach(([k, v]) => (priceLookup.frames[k] = v as number));
      }

      // Seat options
      if (priceData.seatOptions) {
        Object.entries(priceData.seatOptions).forEach(([k, obj]) => {
          // Map seat options by number (1, 2, 3)
          const seatNum = k.replace('OturumSeçenek ', '');
          priceLookup.seats[seatNum] = (obj as any).price ?? 0;
        });
      }

      // Armrest base prices (for KOL 1, KOL 2, etc.)
      if (priceData.armrestBase) {
        Object.entries(priceData.armrestBase).forEach(([k, obj]) => {
          // Map "Base 1" to "KOL 1"
          const kolNum = k.replace('Base ', 'KOL ');
          priceLookup.arms[kolNum] = (obj as any).price ?? 0;
        });
      }

      // Arm extensions (papel, klapa)
      if (priceData.armrestPapel) {
        // Get the first papel price as they're the same
        const papelPrices = Object.values(priceData.armrestPapel) as any[];
        if (papelPrices.length > 0) {
          priceLookup.armOptions['hasPapel'] = papelPrices[0].price ?? 0;
        }
      }
      if (priceData.armrestKlapa) {
        // Get the first klapa price
        const klapaPrices = Object.values(priceData.armrestKlapa) as any[];
        if (klapaPrices.length > 0) {
          priceLookup.armOptions['hasFrontFlap'] = klapaPrices[0].price ?? 0;
        }
      }
      // For hasKulak, we'll use a default value since it's not in the API response
      priceLookup.armOptions['hasKulak'] = 0; // Or set a specific price if needed

      // Legs
      if (priceData.legOptions) {
        Object.entries(priceData.legOptions).forEach(([k, v]) => {
          priceLookup.legs[k] = v as number;
          // Also map with full names
          priceLookup.legs[`${k} Ahşap`] = v as number;
          priceLookup.legs[`${k} Metal`] = v as number;
        });
      }

      // Wood colors (for legs and frames)
      if (priceData.legColors) {
        Object.entries(priceData.legColors).forEach(([k, v]) => {
          // Map color names to match UI values
          const mappedColorNames: Record<string, string> = {
            'Ceviz': 'CevizAhsap',
            'Beyaz': 'BeyazAhsap',
            'Ekru': 'EkruAhsap',
            'Gri': 'GriAhsap',
            'Siyah': 'AntrasitAhsap', // Map Siyah to AntrasitAhsap
            'Sarı Eskitme': 'SariEskitmeAhsap',
            'Siyah Eskitme': 'GriEskitmeAhsap', // Map Siyah Eskitme to GriEskitmeAhsap
            'Ceviz Eskitme': 'CevizEskitmeAhsap'
          };

          const mappedKey = mappedColorNames[k] || `${k}Ahsap`;
          priceLookup.woodColors[mappedKey] = v as number;
        });
      }
      if (priceData.lowerFrameColors) {
        Object.entries(priceData.lowerFrameColors).forEach(([k, v]) => {
          // Map color names to match UI values
          const mappedColorNames: Record<string, string> = {
            'Ceviz': 'CevizAhsap',
            'Beyaz': 'BeyazAhsap',
            'Ekru': 'EkruAhsap',
            'Gri': 'GriAhsap',
            'Siyah': 'AntrasitAhsap', // Map Siyah to AntrasitAhsap
            'Sarı Eskitme': 'SariEskitmeAhsap',
            'Siyah Eskitme': 'GriEskitmeAhsap', // Map Siyah Eskitme to GriEskitmeAhsap
            'Ceviz Eskitme': 'CevizEskitmeAhsap'
          };

          const mappedKey = mappedColorNames[k] || `${k}Ahsap`;
          priceLookup.woodColors[mappedKey] = v as number;
        });
      }

      // Fabrics
      if (priceData.fabrics) {
        Object.entries(priceData.fabrics).forEach(([fabricType, colors]) => {
          Object.entries(colors as any).forEach(([, price]) => {
            priceLookup.fabrics[fabricType] = Math.max(priceLookup.fabrics[fabricType] || 0, price as number);
          });
        });
      }

      // Pillows
      if (priceData.backPillows) {
        Object.entries(priceData.backPillows).forEach(([k, obj]) => {
          priceLookup.pillows[k] = (obj as any).price ?? 0;
        });
      }
      if (priceData.cushions) {
        Object.entries(priceData.cushions).forEach(([k, obj]) => {
          priceLookup.pillows[k] = (obj as any).price ?? 0;
        });
      }

      // Store general outcome and profit rate for later use
      if (priceData.generalOutcomeArmchair) {
        (priceLookup as any).generalOutcomeArmchair = priceData.generalOutcomeArmchair;
      }
      if (priceData.profitRate) {
        (priceLookup as any).profitRate = priceData.profitRate;
      }
      if (priceData.fixedCost) {
        (priceLookup as any).fixedCost = priceData.fixedCost;
      }
    } else {
      // Fallback: original array-based structure
      if (priceData.frames && Array.isArray(priceData.frames)) {
        priceData.frames.forEach((item: PartPrice) => (priceLookup.frames[item.id] = item.price));
      }
      if (priceData.fabrics && Array.isArray(priceData.fabrics)) {
        priceData.fabrics.forEach((item: PartPrice) => (priceLookup.fabrics[item.id] = item.price));
      }
      if (priceData.legs && Array.isArray(priceData.legs)) {
        priceData.legs.forEach((item: PartPrice) => {
          const legId = item.id.split(' ')[0];
          priceLookup.legs[legId] = item.price;
          priceLookup.legs[item.id] = item.price;
        });
      }
      if (priceData.seats && Array.isArray(priceData.seats)) {
        priceData.seats.forEach((item: PartPrice) => (priceLookup.seats[item.id] = item.price));
      }
      if (priceData.arms && Array.isArray(priceData.arms)) {
        priceData.arms.forEach((item: PartPrice) => (priceLookup.arms[item.id] = item.price));
      }
      if (priceData.armOptions && Array.isArray(priceData.armOptions)) {
        priceData.armOptions.forEach((item: PartPrice) => (priceLookup.armOptions[item.id] = item.price));
      }
      if (priceData.woodColors && Array.isArray(priceData.woodColors)) {
        priceData.woodColors.forEach((item: PartPrice) => (priceLookup.woodColors[item.id] = item.price));
      }
      if (priceData.pillows && Array.isArray(priceData.pillows)) {
        priceData.pillows.forEach((item: PartPrice) => (priceLookup.pillows[item.id] = item.price));
      }
    }
    console.log('Processed price lookup:', priceLookup);
    setAllPartPrices(priceLookup);
    setPricingData(priceData); // Store the raw pricing data
    // Calculate initial prices with raw pricing data
    calculateTotalPrice(priceData);
  }, []);
  // Fetch all prices on component mount
  const fetchAllPartPrices = useCallback(async () => {
    try {
      setIsLoadingPrice(true);

      try {
        // Try to call actual API first
        const pricingDto = await backendService.getPricingData();
        console.log('Received pricing data from API:', pricingDto);
        processPriceData(pricingDto);
      } catch (apiError) {
        console.warn('Pricing API call failed:', apiError);
        // Use mock data if API call fails
        processPriceData({});
      }
    } catch (error) {
      console.error('Error in price handling:', error);

    } finally {
      setIsLoadingPrice(false);
    }
  }, [processPriceData]);

  // This will be moved after calculateTotalPrice definition

  // Calculate total price based on selected parts using new backend structure
  const calculateTotalPrice = useCallback((pricingData: any = {}, currentDesign = design, currentPillows = pillows) => {
    try {
      console.log('calculateTotalPrice called with pricing data:', pricingData);
      console.log('Current design:', currentDesign);
      console.log('Current pillows:', currentPillows);

      let totalPrice = 0;

      // 1. Start with general outcome (fixed cost) based on furniture type
      if (currentDesign.furnitureType === 'armchair') {
        totalPrice += pricingData.generalOutcomeArmchair || 0;
      } else if (currentDesign.furnitureType === 'bergere') {
        totalPrice += pricingData.generalOutcomeBergere || 0;
      }

      // 2. Get fabric price per meter based on selected kartela and color
      let fabricPricePerMeter = 0;
      const fabricKartela = `Kartela ${currentDesign.fabric.type.replace('cartela', '')}`;
      if (pricingData.fabrics && pricingData.fabrics[fabricKartela]) {
        fabricPricePerMeter = pricingData.fabrics[fabricKartela][currentDesign.fabric.color] || 0;
      }

      // 3. Calculate seat price and fabric usage (price + fabric metraj * fabric price)
      const seatKey = `OturumSeçenek ${currentDesign.seat.type}`;
      if (pricingData.seatOptions && pricingData.seatOptions[seatKey]) {
        const seatData = pricingData.seatOptions[seatKey];
        totalPrice += seatData.price || 0;

        // Seat fabric calculation: (seat fabric metraj + fixed fabric amount) * fabric price per meter
        const totalSeatFabricAmount = (seatData.fabricAmount || 0) + (pricingData.fixedFabricAmount || 0);
        totalPrice += totalSeatFabricAmount * fabricPricePerMeter;
      }

      // 4. Calculate armrest price and fabric usage (if user selected armrest options)
      const armNumber = currentDesign.arm.type.replace('KOL ', '');
      if (pricingData.armrestBase && pricingData.armrestBase[armNumber]) {
        const armData = pricingData.armrestBase[armNumber];
        totalPrice += armData.price || 0;

        // Armrest fabric calculation: (armrest fabric metraj + fixed fabric amount) * fabric price per meter
        const totalArmrestFabricAmount = (armData.fabricAmount || 0) + (pricingData.fixedFabricAmount || 0);
        totalPrice += totalArmrestFabricAmount * fabricPricePerMeter;
      }

      // 5. Add lower frame fixed price + color multiplier
      const lowerFrameBasePrice = pricingData.lowerFrameFixedPrice || 0;
      let lowerFrameColorMultiplier = 1;
      if (pricingData.lowerFrame && pricingData.lowerFrame[currentDesign.frame.color]) {
        lowerFrameColorMultiplier = pricingData.lowerFrame[currentDesign.frame.color];
      }
      totalPrice += lowerFrameBasePrice * lowerFrameColorMultiplier;

      // 6. Add leg price based on type and color (each leg has its own price + color price)
      const legNumber = currentDesign.leg.type.split(' ')[0]; // Extract "1" from "1 Ahşap"
      if (pricingData.legOptions && pricingData.legOptions[legNumber]) {
        const legColorPrice = pricingData.legOptions[legNumber][currentDesign.leg.color] || 0;
        totalPrice += legColorPrice;
      }

      // 7. Add back pillow price if selected (with quantity and fabric calculations)
      if (currentPillows.backPillow.type && currentPillows.backPillow.type !== 'Yok' && currentPillows.backPillow.quantity > 0) {
        if (pricingData.backPillows && pricingData.backPillows[currentPillows.backPillow.type]) {
          const backPillowData = pricingData.backPillows[currentPillows.backPillow.type];

          // Calculate back pillow price per piece * quantity
          const backPillowBasePrice = (backPillowData.price || 0) * currentPillows.backPillow.quantity;
          totalPrice += backPillowBasePrice;

          // Get fabric price for back pillow based on selected kartela
          let backPillowFabricPricePerMeter = 0;
          if (pricingData.fabrics && pricingData.fabrics[currentPillows.backPillow.fabric]) {
            backPillowFabricPricePerMeter = pricingData.fabrics[currentPillows.backPillow.fabric][currentPillows.backPillow.color] || 0;
          }

          // Calculate fabric cost: (back pillow fabric amount + fixed fabric amount) * fabric price per meter * quantity
          const totalBackPillowFabricAmount = (backPillowData.fabricAmount || 0) + (pricingData.fixedFabricAmount || 0);
          const backPillowFabricCost = totalBackPillowFabricAmount * backPillowFabricPricePerMeter * currentPillows.backPillow.quantity;
          totalPrice += backPillowFabricCost;

          console.log(`Back Pillow: Base price ${backPillowBasePrice}, Fabric cost ${backPillowFabricCost}, Quantity ${currentPillows.backPillow.quantity}`);
        }
      }

      // 8. Add decorative cushion prices (Kırlent 1, 2, 3) with quantity and fabric calculations
      [currentPillows.pillow1, currentPillows.pillow2, currentPillows.pillow3].forEach(pillow => {
        if (pillow.cushion && pillow.cushion !== 'Yok' && pillow.quantity > 0) {
          // Map cushion sizes to Kırlent types for pricing lookup
          let cushionKey = pillow.cushion;
          if (cushionKey === '50*50') cushionKey = 'Kırlent 1';
          else if (cushionKey === '60*60') cushionKey = 'Kırlent 2';
          else if (cushionKey === '60*45') cushionKey = 'Kırlent 3';

          if (pricingData.cushions && pricingData.cushions[cushionKey]) {
            const cushionData = pricingData.cushions[cushionKey];

            // Calculate cushion price per piece * quantity
            const cushionBasePrice = (cushionData.price || 0) * pillow.quantity;
            totalPrice += cushionBasePrice;

            // Get fabric price for this specific cushion based on selected kartela
            let cushionFabricPricePerMeter = 0;
            if (pricingData.fabrics && pricingData.fabrics[pillow.fabric]) {
              cushionFabricPricePerMeter = pricingData.fabrics[pillow.fabric][pillow.color] || 0;
            }

            // Calculate fabric cost: (cushion fabric amount + fixed fabric amount) * fabric price per meter * quantity
            const totalCushionFabricAmount = (cushionData.fabricAmount || 0) + (pricingData.fixedFabricAmount || 0);
            const cushionFabricCost = totalCushionFabricAmount * cushionFabricPricePerMeter * pillow.quantity;
            totalPrice += cushionFabricCost;

            console.log(`Cushion ${cushionKey}: Base price ${cushionBasePrice}, Fabric cost ${cushionFabricCost}, Quantity ${pillow.quantity}`);
          }
        }
      });

      // 9. For bergere, add bergere-specific costs (easy calculation: price + metraj * fabric price)
      if (currentDesign.furnitureType === 'bergere' && pricingData.bergereOptions) {
        // Use skeleton type if available, otherwise default to 'Bergere 1'
        const bergereType = currentDesign.skeleton?.type ? `Bergere ${currentDesign.skeleton.type}` : 'Bergere 1';
        if (pricingData.bergereOptions[bergereType]) {
          const bergereData = pricingData.bergereOptions[bergereType];
          totalPrice += bergereData.price || 0;
          // Bergere fabric calculation: (fixed fabric amount + bergere metraj) * fabric price per meter
          const totalBergereFabricAmount = (pricingData.fixedFabricAmount || 0) + (bergereData.fabricAmount || 0);
          totalPrice += totalBergereFabricAmount * fabricPricePerMeter;
        }
      }

      // 10. Apply profit rate from cost management
      const baseProfitRate = pricingData.profitRate || 0;
      totalPrice = totalPrice * (1 + baseProfitRate / 100);

      // 11. Apply store-specific profit margin (if available from store settings)
      // This would typically come from the authenticated store's settings
      // For now, we'll use a default or fetch from localStorage/auth context
      const storeSettings = JSON.parse(localStorage.getItem('storeSettings') || '{}');
      const storeProfitMargin = storeSettings.profitMargin || 0;
      if (storeProfitMargin > 0) {
        totalPrice = totalPrice * (1 + storeProfitMargin / 100);
        console.log(`Applied store profit margin: ${storeProfitMargin}%`);
      }

      console.log('Final calculated price:', totalPrice);

      // Update price breakdown
      const framePrice = pricingData.lowerFrame?.[currentDesign.frame.color] || 0;
      const fabricPrice = fabricPricePerMeter * 5; // Approximate total fabric usage
      const legPrice = pricingData.legOptions?.[legNumber]?.[currentDesign.leg.color] || 0;
      const cushionPrice = 0; // This would need more detailed calculation
      const additionalOptionsPrice = 0;
      const skeletonPrice = 0;

      setPriceBreakdown({
        framePrice,
        fabricPrice,
        legPrice,
        cushionPrice,
        additionalOptionsPrice,
        skeletonPrice,
        totalPrice,
      });

      // Update price breakdown
      setPriceBreakdown({
        framePrice,
        fabricPrice,
        legPrice,
        cushionPrice,
        additionalOptionsPrice,
        skeletonPrice,
        totalPrice,
      });
    } catch (error) {
      console.error("Error calculating price:", error);
      // Set fallback price
      setPriceBreakdown((prev) => ({
        ...prev,
        totalPrice: 40000, // Simple fallback
      }));
    }
  }, []); // Removed dependencies to avoid circular updates

  // Immediately calculate price when prices are loaded
  useEffect(() => {
    if (Object.keys(allPartPrices).length > 0 &&
      (Object.keys(allPartPrices.frames).length > 0 ||
        Object.keys(allPartPrices.fabrics).length > 0)) {
      console.log('Prices loaded, calculating initial price');
      calculateTotalPrice(pricingData, design, pillows);

      // If saved design was loaded but price wasn't calculated yet, calculate it now
      if (savedDesignLoaded) {
        setTimeout(() => {
          console.log('Recalculating price for saved design after prices loaded');
          calculateTotalPrice(pricingData, design, pillows);
        }, 200);
      }
    }
  }, [allPartPrices, savedDesignLoaded, design, pillows, calculateTotalPrice]); // Added dependencies

  // Fetch store settings for profit margin calculation
  useEffect(() => {
    const fetchStoreSettings = async () => {
      try {
        const settings = await apiService.get('/StoreSettings');
        localStorage.setItem('storeSettings', JSON.stringify(settings));
        console.log('Store settings loaded:', settings);

        // Recalculate price with store settings
        if (Object.keys(pricingData).length > 0) {
          calculateTotalPrice(pricingData, design, pillows);
        }
      } catch (error) {
        console.warn('Could not fetch store settings:', error);
      }
    };

    fetchStoreSettings();
  }, []); // Run once on component mount

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      // Find the container element that wraps the Babylon scene
      const sceneContainer = document.querySelector('.relative.w-full.h-full');
      if (sceneContainer instanceof HTMLElement && sceneContainer?.requestFullscreen) {
        sceneContainer.requestFullscreen().catch((err) => {
          console.error('Error attempting to enable fullscreen:', err);
        });
      } else {
        console.warn('Could not find scene container element for fullscreen');
      }
    } else {
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch((err) => {
          console.error('Error attempting to exit fullscreen:', err);
        });
      }
    }
  };

  // Capture 3D scene as image
  const capture3DScene = (): Promise<string> => {
    return new Promise((resolve) => {
      try {
        // Find the canvas element that contains the 3D scene
        const canvasElement = document.querySelector('canvas') as HTMLCanvasElement;
        if (canvasElement) {
          // Convert canvas to data URL
          const dataURL = canvasElement.toDataURL('image/png');
          resolve(dataURL);
        } else {
          // Fallback to placeholder image
          resolve('/logo.png');
        }
      } catch (error) {
        console.error('Error capturing 3D scene:', error);
        resolve('/logo.png');
      }
    });
  };

  // Other handlers
  const handlePrint = async () => {
    const printWindow = window.open('', '_blank', 'width=1024,height=768');
    if (!printWindow) return;

    // Capture the 3D scene
    const sceneImage = await capture3DScene();

    // Format currency
    const formatCurrency = (amount: number): string => {
      return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY'
      }).format(amount);
    };

    // Get furniture type in Turkish
    const furnitureTypeText = design.furnitureType === 'armchair' ? 'Koltuk' : 'Berjer';

    // Create summary of furniture configuration
    const generateSummary = () => {
      return `
        <div class="design-summary">
          <div class="design-image" style="text-align: center; margin-bottom: 20px;">
            <img src="/logo.png" alt="Kapsül Mobilya" style="max-width: 150px; margin-bottom: 15px;">
            <div class="furniture-preview" style="border: 1px solid #ddd; border-radius: 8px; padding: 10px; margin: 10px 0; background: #f9f9f9;">
              <img src="${sceneImage}" alt="3D Furniture Preview" style="max-width: 100%; height: 300px; object-fit: contain; border-radius: 5px; background: white;">
            </div>
          </div>
          <h3 style="margin: 15px 0 10px 0;">Tasarım ve Fiyat Detayları</h3>
          <table class="combined-table">
            <tr class="section-header">
              <th colspan="2">Tasarım Özeti</th>
              <th>Fiyat</th>
            </tr>
            <tr>
              <td><strong>Müşteri:</strong></td>
              <td>${customerInfo.firstName} ${customerInfo.lastName}</td>
              <td rowspan="8" style="vertical-align: top; padding: 10px;">
                <table class="price-breakdown" style="width: 100%;">
                  <tr>
                    <td>Kasa</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.framePrice)}</td>
                  </tr>
                  <tr>
                    <td>Kumaş</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.fabricPrice)}</td>
                  </tr>
                  <tr>
                    <td>Ayaklar</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.legPrice)}</td>
                  </tr>
                  <tr>
                    <td>Kırlentler</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.cushionPrice)}</td>
                  </tr>
                  <tr>
                    <td>Ek Seçenekler</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.additionalOptionsPrice)}</td>
                  </tr>
                  <tr class="total-row" style="border-top: 2px solid #333; font-weight: bold;">
                    <td>TOPLAM</td>
                    <td style="text-align: right; font-size: 1.2em;">${formatCurrency(priceBreakdown.totalPrice)}</td>
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <td><strong>Ürün:</strong></td>
              <td>${furnitureTypeText}</td>
            </tr>
            <tr>
              <td><strong>Kumaş:</strong></td>
              <td>${design.fabric.type} - ${design.fabric.color}</td>
            </tr>
            <tr>
              <td><strong>Kol Tipi:</strong></td>
              <td>${design.arm.type}</td>
            </tr>
            <tr>
              <td><strong>Ayak Tipi:</strong></td>
              <td>${design.leg.type}</td>
            </tr>
            <tr>
              <td><strong>Oturum:</strong></td>
              <td>${design.seat.type}</td>
            </tr>
            <tr>
              <td><strong>Kırlentler:</strong></td>
              <td>${pillows.backPillow}, ${pillows.pillow1.cushion}</td>
            </tr>
            <tr>
              <td><strong>Barkod:</strong></td>
              <td>${barcodeText}</td>
            </tr>
          </table>
        </div>
      `;
    };

    const htmlContent = `
      <html><head><title>Mobilya Tasarım Bilgileri</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 15px; margin: 0; }
        h2 { margin-top: 0; color: #333; font-size: 20px; text-align: center; }
        h3 { color: #555; font-size: 16px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        td, th { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f5f5f5; text-align: left; font-weight: bold; }
        .combined-table { margin-top: 10px; }
        .combined-table td { padding: 6px 10px; }
        .section-header th { background-color: #e0e0e0; text-align: center; font-size: 14px; }
        .price-breakdown { border: none; }
        .price-breakdown td { border: none; padding: 4px 8px; font-size: 13px; }
        .price-breakdown tr:nth-child(even) { background-color: #f9f9f9; }
        .total-row { background-color: #f0f0f0; }
        .content-container { max-width: 800px; margin: 0 auto; }
        .footer { margin-top: 20px; font-size: 0.8em; color: #777; text-align: center; }
        @media print {
          body { padding: 10px; }
          @page { size: A4; margin: 1cm; }
          .content-container { max-width: 100%; }
        }
      </style>
      </head><body>
      <div class="content-container">
        <h2>Kapsül Mobilya - Tasarım Detayları</h2>

        ${generateSummary()}

        <div class="footer">
          <p>Tarih: ${new Date().toLocaleDateString('tr-TR')} | Kapsül Mobilya &copy; ${new Date().getFullYear()}</p>
        </div>
      </div>
      </body></html>`;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => printWindow.print(), 500);
  };

  const handleSaveDesign = async () => {
    try {
      // Prepare the design data to save in the format expected by backend
      const cartDesign = {
        // Same structure as handleAddToCart
        armchairType: design.furnitureType,
        baseFrame: design.frame.type,
        legs: design.leg.type,
        legFabric: {
          option: design.leg.type,
          color: design.leg.color
        },
        woodVeneer: design.frame.type,
        woodVeneerFabric: {
          option: design.frame.type,
          color: design.frame.color
        },
        mainAhsapFabric: {
          option: design.frame.type,
          color: design.frame.color
        },
        armrest: {
          main: design.arm.type.replace('KOL ', ''), // Extract number from "KOL 1"
          sub: [
            ...(design.arm.hasPapel ? ['3'] : []),
            ...(design.arm.hasKulak ? ['4'] : []),
            ...(design.arm.hasFrontFlap ? ['5'] : [])
          ].join('-') || 'BOŞ',
          mainAhsap: false
        },
        cushions: [
          // Convert pillows to cushion array format
          ...(pillows.pillow1.cushion && pillows.pillow1.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow1.cushion,
            fabric: pillows.pillow1.fabric,
            color: pillows.pillow1.color
          }] : []),
          ...(pillows.pillow2.cushion && pillows.pillow2.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow2.cushion,
            fabric: pillows.pillow2.fabric,
            color: pillows.pillow2.color
          }] : []),
          ...(pillows.pillow3.cushion && pillows.pillow3.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow3.cushion,
            fabric: pillows.pillow3.fabric,
            color: pillows.pillow3.color
          }] : [])
        ],
        backPillow: pillows.backPillow.type,
        sharedFabric: {
          option: design.fabric.type,
          color: design.fabric.color
        },
        seat: {
          option: design.seat.type,
          sub: 'BOŞ'
        },
        customerName: customerInfo.firstName,
        customerSurname: customerInfo.lastName,
        totalPrice: priceBreakdown.totalPrice,
        barcode: barcodeText,
        model: design.furnitureType
      };

      const savedDesign = {
        name: `${customerInfo.firstName} ${customerInfo.lastName} - ${design.furnitureType}`.trim() || 'Custom Design',
        designJson: JSON.stringify(cartDesign),
        furnitureType: design.furnitureType === 'armchair' ? 'armchair' : 'bergere',
        totalPrice: priceBreakdown.totalPrice
      };

      await backendService.saveDesign(savedDesign);
      toast.success('Tasarım başarıyla kaydedildi!');

      // Navigate to saved designs page
      setTimeout(() => {
        navigate('/store/saved-designs');
      }, 1500);
    } catch (err) {
      toast.error('Tasarım kaydedilemedi');
      console.error('Save design error:', err);
    }
  };

  const handleAddToCart = async () => {
    try {
      // Prepare the design for cart with backend expected field names
      const cartDesign = {
        // Map our field names to backend expected names
        armchairType: design.furnitureType,
        baseFrame: design.frame.type,
        legs: design.leg.type,
        legFabric: {
          option: design.leg.type,
          color: design.leg.color
        },
        woodVeneer: design.frame.type,
        woodVeneerFabric: {
          option: design.frame.type,
          color: design.frame.color
        },
        mainAhsapFabric: {
          option: design.frame.type,
          color: design.frame.color
        },
        armrest: {
          main: design.arm.type,
          sub: 'BOŞ', // Default value for sub
          mainAhsap: false // Default value for mainAhsap
        },
        cushions: [
          // Convert pillows to cushion array format expected by backend
          ...(pillows.pillow1.cushion && pillows.pillow1.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow1.cushion,
            fabric: pillows.pillow1.fabric,
            color: pillows.pillow1.color
          }] : []),
          ...(pillows.pillow2.cushion && pillows.pillow2.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow2.cushion,
            fabric: pillows.pillow2.fabric,
            color: pillows.pillow2.color
          }] : []),
          ...(pillows.pillow3.cushion && pillows.pillow3.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow3.cushion,
            fabric: pillows.pillow3.fabric,
            color: pillows.pillow3.color
          }] : [])
        ],
        backPillow: pillows.backPillow.type,
        sharedFabric: {
          option: design.fabric.type,
          color: design.fabric.color
        },
        seat: {
          option: design.seat.type,
          sub: 'BOŞ' // Default value for sub
        },
        customerName: customerInfo.firstName,
        customerSurname: customerInfo.lastName,
        totalPrice: priceBreakdown.totalPrice,
        barcode: barcodeText,
        model: design.furnitureType
      };

      console.log('Sending cart design:', cartDesign);

      await backendService.addCustomDesignToCart(cartDesign);
      toast.success('Sepete eklendi!');

      // Refresh the cart after adding the item
      if (typeof fetchCart === 'function') {
        fetchCart();
      }
    } catch (err) {
      toast.error('Sepete eklenemedi');
      console.error('Cart error:', err);
    }
    updateBabylonDesign();
  };

  // Fetch all prices on component mount
  useEffect(() => {
    fetchAllPartPrices();
  }, [fetchAllPartPrices]);

  // Helper function to parse arm sub options
  const parseArmSubOptions = (sub: string | string[] | undefined) => {
    const subStr = Array.isArray(sub) ? sub.join('-') : (sub || '');
    return {
      hasPapel: subStr.includes('3'),
      hasKulak: subStr.includes('4'),
      hasFrontFlap: subStr.includes('5')
    };
  };

  // Load saved design if provided
  useEffect(() => {
    if (location.state?.savedDesign) {
      const saved = location.state.savedDesign;
      console.log('Loading saved design:', saved);

      // Update customer info if available
      if (saved.customerName || saved.customerSurname) {
        setCustomerInfo({
          firstName: saved.customerName || '',
          lastName: saved.customerSurname || ''
        });
      }

      // Update design state based on saved design structure
      if (saved.model || saved.furnitureType || saved.armchairType) {
        console.log('Full saved design data:', saved);
        console.log('Updating design from saved data:', {
          fabric: saved.sharedFabric,
          frame: { woodVeneer: saved.woodVeneer, baseFrame: saved.baseFrame, woodVeneerFabric: saved.woodVeneerFabric },
          arm: { armrest: saved.armrest, mainAhsapFabric: saved.mainAhsapFabric },
          leg: { legs: saved.legs, legFabric: saved.legFabric },
          seat: saved.seat
        });

        setDesign(prevDesign => ({
          ...prevDesign,
          furnitureType: saved.model || saved.furnitureType || 'armchair',
          fabric: {
            type: saved.sharedFabric?.option || saved.fabric?.type || 'cartela1',
            color: saved.sharedFabric?.color || saved.fabric?.color || '1'
          },
          frame: {
            type: saved.woodVeneer || saved.baseFrame || saved.frame?.type || '1',
            color: saved.woodVeneerFabric?.option || saved.woodVeneerFabric?.color || saved.frame?.color || 'CevizAhsap'
          },
          arm: {
            type: saved.armrest?.main ? `KOL ${saved.armrest.main}` : saved.arm?.type || 'KOL 1',
            color: saved.mainAhsapFabric?.option || saved.mainAhsapFabric?.color || saved.arm?.color || 'CevizAhsap',
            ...parseArmSubOptions(saved.armrest?.sub)
          },
          seat: {
            type: saved.seat?.option || saved.seat?.type || '1',
            color: saved.sharedFabric?.color || saved.seat?.color || 'brown',
            options: saved.seat?.options || {
              cektirme: false,
              tekParca: false,
              bombe: false,
              kirisiklik: false,
              kulak: false
            }
          },
          leg: {
            type: saved.legs || saved.leg?.type || '1 Ahşap',
            color: saved.legFabric?.option || saved.legFabric?.color || saved.leg?.color || 'CevizAhsap'
          }
        }));

        // Update babylon design
        setBabylonDesign(prevDesign => ({
          ...prevDesign,
          furnitureType: saved.model || saved.furnitureType || 'armchair',
          frameColor: saved.woodVeneerFabric?.option || 'CevizAhsap',
          upholstery: saved.sharedFabric?.option || 'brown',
          legType: saved.legs || '1 Ahşap',
          sharedFabric: saved.sharedFabric?.option || 'brown',
          legs: saved.legs ? saved.legs.split(' ')[0] : '1',
          legFabric: saved.legFabric?.option || 'CevizAhsap',
          lowerFrame: saved.baseFrame || '1',
          woodVeneer: saved.woodVeneer || '1',
          woodVeneerFabric: saved.woodVeneerFabric?.option || 'CevizAhsap',
          armrest: {
            main: saved.armrest?.main || '1',
            sub: saved.armrest?.sub || []
          },
          seat: {
            type: saved.seat?.option || '1',
            color: saved.sharedFabric?.color || 'brown',
            options: saved.seat?.options || {
              cektirme: false,
              tekParca: false,
              bombe: false,
              kirisiklik: false,
              kulak: false
            }
          },
          fabric: {
            type: saved.sharedFabric?.option || 'cartela1',
            color: saved.sharedFabric?.color || '1'
          }
        }));

        // Update pillows if available
        if (saved.backPillow || saved.cushions) {
          setPillows({
            backPillow: saved.backPillow || 'Silikon Elyaf',
            pillow1: saved.cushions?.[0]?.cushionType || '50*50',
            pillow2: saved.cushions?.[1]?.cushionType || '50*50',
            pillow3: saved.cushions?.[2]?.cushionType || '50*50'
          });
        }

        // Mark that saved design has been loaded
        setSavedDesignLoaded(true);

        // If prices are already loaded, calculate the price immediately
        if (Object.keys(allPartPrices.frames).length > 0 || Object.keys(allPartPrices.fabrics).length > 0) {
          setTimeout(() => {
            console.log('Calculating price after loading saved design');
            // Use the design state that was just set in the setDesign call above
            const updatedDesign = {
              ...design,
              furnitureType: saved.model || saved.furnitureType || 'armchair',
              fabric: {
                type: saved.sharedFabric?.option || saved.fabric?.type || 'cartela1',
                color: saved.sharedFabric?.color || saved.fabric?.color || '1'
              },
              frame: {
                type: saved.woodVeneer || saved.baseFrame || saved.frame?.type || '1',
                color: saved.woodVeneerFabric?.option || saved.woodVeneerFabric?.color || saved.frame?.color || 'CevizAhsap'
              },
              arm: {
                type: saved.armrest?.main ? `KOL ${saved.armrest.main}` : saved.arm?.type || 'KOL 1',
                color: saved.mainAhsapFabric?.option || saved.mainAhsapFabric?.color || saved.arm?.color || 'CevizAhsap',
                ...parseArmSubOptions(saved.armrest?.sub)
              },
              seat: {
                type: saved.seat?.option || saved.seat?.type || '1',
                color: saved.sharedFabric?.color || saved.seat?.color || 'brown',
                options: saved.seat?.options || {
                  cektirme: false,
                  tekParca: false,
                  bombe: false,
                  kirisiklik: false,
                  kulak: false
                }
              },
              leg: {
                type: saved.legs || saved.leg?.type || '1 Ahşap',
                color: saved.legFabric?.option || saved.legFabric?.color || saved.leg?.color || 'CevizAhsap'
              }
            };
            calculateTotalPrice(pricingData, updatedDesign, pillows);
          }, 100);
        }
      }
    }
  }, [location.state, allPartPrices, pillows, calculateTotalPrice]); // Added missing dependencies

  // Recalculate price when design or pillows change
  useEffect(() => {
    // Only calculate price if prices are loaded AND either it's not a saved design or the saved design has been loaded
    if ((Object.keys(allPartPrices.frames).length > 0 || Object.keys(allPartPrices.fabrics).length > 0) &&
      (!location.state?.savedDesign || savedDesignLoaded)) {
      console.log('Recalculating price due to design/pillows change', {
        hasPrices: Object.keys(allPartPrices.frames).length > 0,
        savedDesignLoaded,
        design
      });
      calculateTotalPrice(pricingData, design, pillows);
    }
  }, [design, pillows, calculateTotalPrice, allPartPrices, savedDesignLoaded, location.state]);

  // Call updateBabylonDesign on initial load and when design changes
  useEffect(() => {
    updateBabylonDesign();
  }, [
    design.furnitureType,
    design.fabric,
    design.frame,
    design.arm,
    design.seat,
    design.leg,
    pillows.backPillow.type,
    updateBabylonDesign
  ]);

  // Helper function to get fabric code
  const getFabricCode = (fabricType: string): string => {
    const fabricCodes: Record<string, string> = {
      'brown': 'BR',
      'greenCotton': 'GC',
      'grey': 'GR',
      'cartela1': 'K1',
      'cartela2': 'K2',
      'cartela3': 'K3',
      'cartela4': 'K4',
      'cartela5': 'K5',
      'cartela6': 'K6',
      'cartela7': 'K7',
      'cartela8': 'K8',
      'cartela9': 'K9',
      'cartela10': 'K10',
      'cartela11': 'K11',
      'cartela12': 'K12',
    };
    return fabricCodes[fabricType] || fabricType;
  };

  // Helper function to get wood color index
  const getWoodColorIndex = (color: string): string => {
    const colorIndices: Record<string, string> = {
      'CevizAhsap': '1',
      'BeyazAhsap': '2',
      'EkruAhsap': '3',
      'GriAhsap': '4',
      'AntrasitAhsap': '5',
      'SariEskitmeAhsap': '6',
      'GriEskitmeAhsap': '7',
      'CevizEskitmeAhsap': '8',
    };
    return colorIndices[color] || '1';
  };

  // Helper function to get metal color index
  const getMetalColorIndex = (color: string): string => {
    const colorIndices: Record<string, string> = {
      'Bronz': '9',
      'Gold': '10',
      'nickel': '11',
    };
    return colorIndices[color] || color;
  };

  // Add useEffect to update barcode when design changes
  useEffect(() => {
    // Get fabric code
    const fabricCode = getFabricCode(design.fabric.type);

    // Get arm options with correct numbering
    const armOptions = [];
    if (design.arm.hasDugme) armOptions.push('1'); // 1 = Düğme Kapiton
    if (design.arm.hasBoğumKapiton) armOptions.push('2'); // 2 = Boğum Kapiton
    if (design.arm.hasPapel) armOptions.push('3'); // 3 = Papel
    if (design.arm.hasKulak) armOptions.push('4'); // 4 = Kulak
    if (design.arm.hasFrontFlap) armOptions.push('5'); // 5 = Ön Klapa

    const armOptionsStr = armOptions.length > 0 ? armOptions.join('-') : '';

    // Get seat options (matching available assets: 1, 3, 4)
    const seatOptions = [];
    if (design.seat.options.cektirme) seatOptions.push('1'); // 1 = Çektirme
    if (design.seat.options.tekParca) seatOptions.push('3'); // 3 = Tek Parça
    if (design.seat.options.bombe) seatOptions.push('4'); // 4 = Bombe
    // Note: kirisiklik (option 2) and kulak are not included as they don't exist in assets
    const seatOptionsStr = seatOptions.length > 0 ? seatOptions.join('-') : '';

    // Extract arm number from "KOL X" format
    const armNumber = design.arm.type.replace('KOL ', '');

    // Extract leg number (e.g., "1 Ahşap" -> "1", "3 Metal" -> "3")
    const legNumber = design.leg.type.split(' ')[0];

    // Get color indices
    const frameColorIndex = getWoodColorIndex(design.frame.color);
    const legColorIndex = design.leg.color.includes('Ahsap')
      ? getWoodColorIndex(design.leg.color)
      : getMetalColorIndex(design.leg.color);

    // Build barcode for armchair
    let barcodeString = '';
    if (design.furnitureType === 'armchair') {
      // For fabric code: if it's a kartela, just show the number part (K1 -> 1, K2 -> 2, etc.)
      let fabricPart = '';
      if (design.fabric.type.startsWith('cartela')) {
        // Extract number from cartela (cartela1 -> 1, cartela12 -> 12)
        const cartelaNumber = design.fabric.type.replace('cartela', '');
        fabricPart = `${cartelaNumber}(${design.fabric.color})`;
      } else {
        // For other fabrics, show code and color
        fabricPart = `${fabricCode}(${design.fabric.color})`;
      }

      // Format: Fabric_Arm(Options)-Seat(Options)-Frame(ColorIndex)-LegNumber-LegColorIndex
      barcodeString = `${fabricPart}_${armNumber}${armOptionsStr ? `(${armOptionsStr})` : ''}-${design.seat.type}${seatOptionsStr ? `(${seatOptionsStr})` : ''}-${design.frame.type}(${frameColorIndex})-${legNumber}-${legColorIndex}`;
    } else if (design.furnitureType === 'bergere') {
      // For bergere: simpler format without arm and frame
      let fabricPart = '';
      if (design.fabric.type.startsWith('cartela')) {
        const cartelaNumber = design.fabric.type.replace('cartela', '');
        fabricPart = `${cartelaNumber}(${design.fabric.color})`;
      } else {
        fabricPart = `${fabricCode}(${design.fabric.color})`;
      }

      const skeletonColorIndex = design.skeleton ? getWoodColorIndex(design.skeleton.color) : '1';
      barcodeString = `B${fabricPart}_${design.seat.type}-${skeletonColorIndex}`;
    }

    setBarcodeText(barcodeString);
  }, [design]); // Only depend on design, not pillows

  // Add useEffect to handle model loading state
  useEffect(() => {
    // Start with loading state
    setIsModelLoading(true);

    // Set a timeout to hide the loading indicator after a reasonable time
    const loadingTimer = setTimeout(() => {
      setIsModelLoading(false);
    }, 3000); // 3 seconds should be enough for most models to load

    // Clean up the timer
    return () => clearTimeout(loadingTimer);
  }, [babylonDesign]); // Re-run when the 3D model design changes

  // Format price to Turkish Lira format
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(price);
  };

  // Memoize the decorative cushions prop to prevent re-renders
  const memoizedDecorativeCushions = React.useMemo(() => ({
    size: "50*50",
    fabric: "static",
    quantity: 1,
  }), []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <main className="relative w-full mx-auto py-4 px-8">
        <h2 className="text-3xl font-extrabold mb-4 bg-gradient-to-r">Mobilya Tasarım Aracı</h2>

        {/* Furniture Type Selection */}
        <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <h3 className="text-xl font-bold text-transparent -mt-2 bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">Mobilya Tipi</h3>
          <select
            className="w-full bg-white border border-gray-300 text-gray-900 placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-300 rounded-lg py-2.5 pl-3 pr-10"
            value={design.furnitureType}
            onChange={handleFurnitureTypeChange}
          >
            <option value="armchair">Koltuk</option>
            <option value="bergere">Berjer</option>
          </select>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-24 gap-6 my-3">
          {/* 3D Model Viewer */}
          <div className="col-span-15">
            <div className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm p-4" style={{ height: '700px' }}>
              <div className="relative w-full h-full">
                <div
                  className="relative w-full h-full"
                  onWheel={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onMouseEnter={(e) => {
                    // Prevent page scrolling when mouse is over the 3D scene
                    document.body.style.overflow = 'hidden';
                  }}
                  onMouseLeave={(e) => {
                    // Re-enable page scrolling when mouse leaves the 3D scene
                    document.body.style.overflow = 'auto';
                  }}
                  style={{ overflow: 'hidden' }}
                >
                  <MemoizedBabylonScene
                    design={babylonDesign}
                    furnitureType={babylonDesign.furnitureType}
                    getPartFilePath={getPartFilePath}
                    backCushion="Silikon Elyaf"
                    decorativeCushions={memoizedDecorativeCushions}
                  />
                  {isModelLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                      <div className="text-center">
                        <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                        <p className="text-purple-600 font-medium">3D Model Yükleniyor...</p>
                      </div>
                    </div>
                  )}
                </div>
                <button
                  className="absolute top-4 right-4 p-2 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 text-gray-700 hover:text-purple-600 z-10"
                  title="enter_fullscreen"
                  onClick={toggleFullscreen}
                >
                  <BsFullscreen size={20} />
                </button>
              </div>
            </div>
            <div className="mt-1.5 flex items-center justify-end bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg p-3 shadow-sm">
              <span className="text-lg font-medium text-gray-700">
                Toplam Fiyat:
                {isLoadingPrice ? (
                  <span className="text-xl text-gray-400">Yükleniyor...</span>
                ) : (
                  <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {formatPrice(priceBreakdown.totalPrice)}
                  </span>
                )}
              </span>
            </div>
          </div>

          {/* Customization Panel - rest of the component remains the same */}
          <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 col-span-9">
            {/* Fabric section */}
            <div className="col-span-2">
              <h3 className="text-lg font-semibold mb-1 -mt-2 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kumaş</h3>
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kumaş Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md "
                    value={design.fabric.type}
                    onChange={handleFabricTypeChange}
                  >
                    <option value="cartela1">Kartela 1</option>
                    <option value="cartela2">Kartela 2</option>
                    <option value="cartela3">Kartela 3</option>
                    <option value="cartela4">Kartela 4</option>
                    <option value="cartela5">Kartela 5</option>
                    <option value="cartela6">Kartela 6</option>
                    <option value="cartela7">Kartela 7</option>
                    <option value="cartela8">Kartela 8</option>
                    <option value="cartela9">Kartela 9</option>
                    <option value="cartela10">Kartela 10</option>
                    <option value="cartela11">Kartela 11</option>
                    <option value="cartela12">Kartela 12</option>
                  </select>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Renk Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.fabric.color}
                    onChange={handleFabricColorChange}
                  >
                    {/* Show different number of colors based on selected cartela */}
                    {design.fabric.type === 'cartela12' ? (
                      // Cartela 12 has 6 colors
                      Array.from({ length: 6 }, (_, i) => (
                        <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                      ))
                    ) : design.fabric.type === 'cartela8' ? (
                      // Cartela 8 has 10 colors
                      Array.from({ length: 10 }, (_, i) => (
                        <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                      ))
                    ) : (
                      // All other cartelas have 9 colors
                      Array.from({ length: 9 }, (_, i) => (
                        <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                      ))
                    )}
                  </select>
                </div>
              </div>
            </div>

            {/* Arms section - Hide for bergere */}
            {design.furnitureType !== 'bergere' && (
              <div className="col-span-1 mt-1.5">
                <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kol</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kol Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md "
                    value={design.arm.type}
                    onChange={handleArmTypeChange}
                  >
                    <option value="KOL 1">KOL 1</option>
                    <option value="KOL 2">KOL 2</option>
                    <option value="KOL 3">KOL 3</option>
                    <option value="KOL 4">KOL 4</option>
                    <option value="KOL 5">KOL 5</option>
                    <option value="KOL 6">KOL 6</option>
                    <option value="KOL 7">KOL 7</option>
                  </select>
                </div>
                <div className="mt-1.5 flex flex-col flex-wrap gap-y-2">
                  <div>
                    <label className="inline-flex items-center mr-4">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600"
                        checked={design.arm.hasPapel}
                        onChange={(e) => handleArmOptionChange('hasPapel', e.target.checked)}
                      />
                      <span className="ml-2 text-sm text-gray-700">Papel</span>
                    </label>

                    <label className="inline-flex items-center mr-4">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600"
                        checked={design.arm.hasKulak}
                        onChange={(e) => handleArmOptionChange('hasKulak', e.target.checked)}
                      />
                      <span className="ml-2 text-sm text-gray-700">Kulak</span>
                    </label>

                    <label className="inline-flex items-center mr-4">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600"
                        checked={design.arm.hasFrontFlap}
                        onChange={(e) => handleArmOptionChange('hasFrontFlap', e.target.checked)}
                      />
                      <span className="ml-2 text-sm text-gray-700">Ön Klapa</span>
                    </label>
                  </div>
                  <div>
                    {/* Now enabled options for 4 Düğme and 5 Boğum kapiton */}
                    <label className="inline-flex items-center mr-4">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600"
                        checked={design.arm.hasDugme || false}
                        onChange={(e) => handleArmOptionChange('hasDugme', e.target.checked)}
                      />
                      <span className="ml-2 text-sm text-gray-700">4 Düğme</span>
                    </label>

                    <label className="inline-flex items-center mr-4">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600"
                        checked={design.arm.hasBoğumKapiton || false}
                        onChange={(e) => handleArmOptionChange('hasBoğumKapiton', e.target.checked)}
                      />
                      <span className="ml-2 text-sm text-gray-700">5 Boğum Kapiton</span>
                    </label>
                    <div className="w-full"></div>
                  </div>
                </div>
              </div>
            )}

            {/* Rest of your UI components go here - keep the same structure */}
            {/* ... */}

            {/* Seat Options */}
            <div className="col-span-1 mt-1.5">
              <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                {design.furnitureType === 'bergere' ? 'İskelet ve Oturum' : 'Oturum'}
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {design.furnitureType === 'bergere' ? 'İskelet Seç' : 'Oturum Seç'}
                </label>
                <select
                  className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  value={design.seat.type}
                  onChange={handleSeatTypeChange}
                >
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                </select>
              </div>

              {/* Skeleton Color for Bergere */}
              {design.furnitureType === 'bergere' && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    İskelet Rengi
                  </label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.skeleton?.color || 'CevizAhsap'}
                    onChange={(e) => {
                      setDesign(prev => ({
                        ...prev,
                        skeleton: {
                          ...prev.skeleton!,
                          color: e.target.value
                        }
                      }));
                    }}
                  >
                    <option value="CevizAhsap">Ceviz</option>
                    <option value="BeyazAhsap">Beyaz</option>
                    <option value="EkruAhsap">Ekru</option>
                    <option value="GriAhsap">Gri</option>
                    <option value="AntrasitAhsap">Antrasit</option>
                    <option value="SariEskitmeAhsap">Sarı Eskitme</option>
                    <option value="GriEskitmeAhsap">Gri Eskitme</option>
                    <option value="CevizEskitmeAhsap">Ceviz Eskitme</option>
                  </select>
                </div>
              )}

              {design.furnitureType !== 'bergere' && (
                <div className="mt-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Oturum Seçenekleri</label>
                  <div className="flex flex-wrap gap-4">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        id="seat-option-cektirme"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        checked={design.seat.options.cektirme}
                        onChange={() => handleSeatOptionChange('options', 'cektirme')}
                      />
                      <span className="ml-2 text-sm text-gray-700">Çektirme</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        id="seat-option-tekParca"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        checked={design.seat.options.tekParca}
                        onChange={() => handleSeatOptionChange('options', 'tekParca')}
                      />
                      <span className="ml-2 text-sm text-gray-700">Tek Parça</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        id="seat-option-bombe"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        checked={design.seat.options.bombe}
                        onChange={() => handleSeatOptionChange('options', 'bombe')}
                      />
                      <span className="ml-2 text-sm text-gray-700">Bombe</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        id="seat-option-kirisiklik"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded opacity-50 cursor-not-allowed"
                        checked={design.seat.options.kirisiklik}
                        onChange={() => handleSeatOptionChange('options', 'kirisiklik')}
                        disabled
                      />
                      <span className="ml-2 text-sm text-gray-700">Kırışıklık</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        id="seat-option-kulak"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        checked={design.seat.options.kulak}
                        onChange={() => handleSeatOptionChange('options', 'kulak')}
                      />
                      <span className="ml-2 text-sm text-gray-700">Kulak</span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            {/* Frame Options - Hide for bergere */}
            {design.furnitureType !== 'bergere' && (
              <div className="col-span-1 mt-1.5">
                <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Alt Kasa</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Alt Kasa Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.frame.type}
                    onChange={handleFrameTypeChange}
                  >
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Renk Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.frame.color}
                    onChange={handleFrameColorChange}
                  >
                    <option value="CevizAhsap">Ceviz</option>
                    <option value="BeyazAhsap">Beyaz</option>
                    <option value="EkruAhsap">Ekru</option>
                    <option value="GriAhsap">Gri</option>
                    <option value="AntrasitAhsap">Antrasit</option>
                    <option value="SariEskitmeAhsap">Sarı Eskitme</option>
                    <option value="GriEskitmeAhsap">Gri Eskitme</option>
                    <option value="CevizEskitmeAhsap">Ceviz Eskitme</option>
                  </select>
                </div>
              </div>
            )}

            {/* Leg Options - Hide for bergere */}
            {design.furnitureType !== 'bergere' && (
              <div className="col-span-1 mt-1.5">
                <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Ayaklar</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Ayak Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.leg.type}
                    onChange={handleLegTypeChange}
                  >
                    <option value="1 Ahşap">1 Ahşap</option>
                    <option value="2 Ahşap">2 Ahşap</option>
                    <option value="3 Metal">3 Metal</option>
                    <option value="4 Metal">4 Metal</option>
                    <option value="5 Ahşap">5 Ahşap</option>
                    <option value="6 Ahşap">6 Ahşap</option>
                    <option value="7 Metal">7 Metal</option>
                    <option value="8 Metal">8 Metal</option>
                    <option value="9 Metal">9 Metal</option>
                    <option value="10 Metal">10 Metal</option>
                    <option value="11 Ahşap">11 Ahşap</option>
                    <option value="12 Ahşap">12 Ahşap</option>
                    <option value="13 Ahşap">13 Ahşap</option>
                  </select>
                </div>
                <div className="mt-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Renk Seç</label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.leg.color}
                    onChange={handleLegColorChange}
                  >
                    {/* Wood colors - show for wooden legs */}
                    {!design.leg.type.includes('Metal') && (
                      <>
                        <option value="CevizAhsap">Ceviz</option>
                        <option value="BeyazAhsap">Beyaz</option>
                        <option value="EkruAhsap">Ekru</option>
                        <option value="GriAhsap">Gri</option>
                        <option value="AntrasitAhsap">Antrasit</option>
                        <option value="SariEskitmeAhsap">Sarı Eskitme</option>
                        <option value="GriEskitmeAhsap">Gri Eskitme</option>
                        <option value="CevizEskitmeAhsap">Ceviz Eskitme</option>
                      </>
                    )}
                    {/* Metal legs - show all wooden colors except Ceviz + metal colors */}
                    {design.leg.type.includes('Metal') && (
                      <>
                        {/* Wooden colors (excluding Ceviz) */}
                        <option value="BeyazAhsap">Beyaz</option>
                        <option value="EkruAhsap">Ekru</option>
                        <option value="GriAhsap">Gri</option>
                        <option value="AntrasitAhsap">Antrasit</option>
                        <option value="SariEskitmeAhsap">Sarı Eskitme</option>
                        <option value="GriEskitmeAhsap">Gri Eskitme</option>
                        <option value="CevizEskitmeAhsap">Ceviz Eskitme</option>
                        {/* Metal colors */}
                        <option value="Bronz">Bronz</option>
                        <option value="Gold">Gold</option>
                        <option value="nickel">Nikel</option>
                      </>
                    )}
                  </select>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Pillows Section - Hide for bergere */}
        {design.furnitureType !== 'bergere' && (
          <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 mb-6">
            <h3 className="text-lg font-semibold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kırlentler</h3>

            {/* Grid layout for all 4 pillow types */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Back Pillow (Sırt Kırlenti) */}
              <div className="space-y-2">
                <h4 className="text-md font-medium text-gray-700">Sırt Kırlenti</h4>
                <div className="space-y-2">
                  <select
                    className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                    value={pillows.backPillow.type}
                    onChange={(e) => handlePillowChange('backPillow', 'type', e.target.value)}
                  >
                    <option value="Silikon Elyaf">Silikon Elyaf</option>
                    <option value="Sünger">Sünger</option>
                    <option value="Yok">Yok</option>
                  </select>

                  {pillows.backPillow.type !== 'Yok' && (
                    <>
                      <select
                        className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                        value={pillows.backPillow.fabric}
                        onChange={(e) => handlePillowChange('backPillow', 'fabric', e.target.value)}
                      >
                        <option value="Kartela 1">Kartela 1</option>
                        <option value="Kartela 2">Kartela 2</option>
                        <option value="Kartela 3">Kartela 3</option>
                        <option value="Kartela 4">Kartela 4</option>
                        <option value="Kartela 5">Kartela 5</option>
                        <option value="Kartela 6">Kartela 6</option>
                        <option value="Kartela 7">Kartela 7</option>
                        <option value="Kartela 8">Kartela 8</option>
                        <option value="Kartela 9">Kartela 9</option>
                        <option value="Kartela 10">Kartela 10</option>
                        <option value="Kartela 11">Kartela 11</option>
                        <option value="Kartela 12">Kartela 12</option>
                      </select>

                      <select
                        className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                        value={pillows.backPillow.color}
                        onChange={(e) => handlePillowChange('backPillow', 'color', e.target.value)}
                      >
                        {/* Show different number of colors based on selected cartela */}
                        {pillows.backPillow.fabric === 'Kartela 12' ? (
                          Array.from({ length: 6 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        ) : pillows.backPillow.fabric === 'Kartela 8' ? (
                          Array.from({ length: 10 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        ) : (
                          Array.from({ length: 9 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        )}
                      </select>

                      <input
                        type="number"
                        min="0"
                        max="10"
                        className="w-full px-3 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                        value={pillows.backPillow.quantity}
                        onChange={(e) => handlePillowChange('backPillow', 'quantity', parseInt(e.target.value) || 0)}
                        placeholder="Adet"
                      />
                    </>
                  )}
                </div>
              </div>

              {/* Decorative Cushions (Kırlent 1, 2, 3) */}
              {(['pillow1', 'pillow2', 'pillow3'] as const).map((pillowKey, index) => {
                const pillow = pillows[pillowKey];
                return (
                  <div key={pillowKey} className="space-y-2">
                    <h4 className="text-md font-medium text-gray-700">Kırlent {index + 1}</h4>
                    <div className="space-y-2">
                      <select
                        className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                        value={pillow.cushion}
                        onChange={(e) => handlePillowChange(pillowKey, 'cushion', e.target.value)}
                      >
                        <option value="50*50">50*50</option>
                        <option value="60*60">60*60</option>
                        <option value="60*45">60*45</option>
                        <option value="Yok">Yok</option>
                      </select>

                      {pillow.cushion !== 'Yok' && (
                        <>
                          <select
                            className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                            value={pillow.fabric}
                            onChange={(e) => handlePillowChange(pillowKey, 'fabric', e.target.value)}
                          >
                            <option value="Kartela 1">Kartela 1</option>
                            <option value="Kartela 2">Kartela 2</option>
                            <option value="Kartela 3">Kartela 3</option>
                            <option value="Kartela 4">Kartela 4</option>
                            <option value="Kartela 5">Kartela 5</option>
                            <option value="Kartela 6">Kartela 6</option>
                            <option value="Kartela 7">Kartela 7</option>
                            <option value="Kartela 8">Kartela 8</option>
                            <option value="Kartela 9">Kartela 9</option>
                            <option value="Kartela 10">Kartela 10</option>
                            <option value="Kartela 11">Kartela 11</option>
                            <option value="Kartela 12">Kartela 12</option>
                          </select>

                          <select
                            className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                            value={pillow.color}
                            onChange={(e) => handlePillowChange(pillowKey, 'color', e.target.value)}
                          >
                            {/* Show different number of colors based on selected cartela */}
                            {pillow.fabric === 'Kartela 12' ? (
                              Array.from({ length: 6 }, (_, i) => (
                                <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                              ))
                            ) : pillow.fabric === 'Kartela 8' ? (
                              Array.from({ length: 10 }, (_, i) => (
                                <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                              ))
                            ) : (
                              Array.from({ length: 9 }, (_, i) => (
                                <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                              ))
                            )}
                          </select>

                          <input
                            type="number"
                            min="0"
                            max="10"
                            className="w-full px-3 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                            value={pillow.quantity}
                            onChange={(e) => handlePillowChange(pillowKey, 'quantity', parseInt(e.target.value) || 0)}
                            placeholder="Adet"
                          />
                        </>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Customer Information and Action Buttons */}
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mx-4">
            <div>
              <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Müşteri Adı</label>
              <input
                type="text"
                className="w-full pl-3 pr-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={customerInfo.firstName}
                onChange={(e) => handleCustomerInfoChange('firstName', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Müşteri Soyadı</label>
              <input
                type="text"
                className="w-full pl-3 pr-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={customerInfo.lastName}
                onChange={(e) => handleCustomerInfoChange('lastName', e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <button
              onClick={handlePrint}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-gray-100 text-gray-700 hover:bg-gray-200 h-10 py-2 px-4 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-gray-300 focus:ring-offset-2"
            >
              Yazdır
            </button>
            <div className="flex space-x-4">
              <button
                onClick={handleSaveDesign}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-white border-2 border-purple-600 text-purple-600 hover:bg-purple-50 h-10 py-2 px-4 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-300 focus:ring-offset-2"
              >
                Tasarımı Kaydet
              </button>
              <button
                onClick={handleAddToCart}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
              >
                Sepete Ekle
              </button>
            </div>
          </div>

          {/* Barcode and Total Price */}
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold text-gray-700 mb-1">Barkod</h3>
              <div className="relative group">
                <div className="invisible group-hover:visible absolute left-0 bottom-full mb-2 p-4 bg-white rounded-lg shadow-xl border border-gray-200 z-50 w-96">
                  <h4 className="font-semibold text-gray-800 mb-2">Barkod Açıklaması</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-semibold">Kumaş Kodu:</span> Kumaş tipi ve renk numarası</p>
                    <p><span className="font-semibold">Kol Kodu:</span> Kol tipi ve ek parçalar</p>
                    <p><span className="font-semibold">Oturum Kodu:</span> Oturum tipi ve ek parçalar</p>
                    <p><span className="font-semibold">Alt Kasa:</span> Alt kasa ve ahşap kaplama numarası</p>
                    <p><span className="font-semibold">Ayak Rengi:</span> Ayak renk indeksi</p>
                    <p><span className="font-semibold">Ahşap Rengi:</span> Ahşap renk indeksi</p>
                  </div>
                  <div className="absolute left-4 bottom-[-8px] w-4 h-4 bg-white border-r border-b border-gray-200 transform rotate-45"></div>
                </div>
                <p className="text-base text-gray-900 font-mono bg-gray-50 px-4 py-2 rounded-md border border-gray-200 cursor-help">{barcodeText}</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <h3 className="text-lg font-semibold text-gray-700 mb-1">Toplam Fiyat</h3>
              <div>
                {isLoadingPrice ? (
                  <span className="text-lg text-gray-400">Yükleniyor...</span>
                ) : (
                  <p className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {formatPrice(priceBreakdown.totalPrice)}
                  </p>
                )}
              </div>

              {/* Price breakdown */}
              <div className="mt-2 text-sm text-gray-600 text-right">
                <table className="w-48 text-right ml-auto">
                  <tbody>
                    <tr>
                      <td className="pr-2">Kasa:</td>
                      <td>{formatPrice(priceBreakdown.framePrice)}</td>
                    </tr>
                    <tr>
                      <td className="pr-2">Kumaş:</td>
                      <td>{formatPrice(priceBreakdown.fabricPrice)}</td>
                    </tr>
                    <tr>
                      <td className="pr-2">Ayaklar:</td>
                      <td>{formatPrice(priceBreakdown.legPrice)}</td>
                    </tr>
                    <tr>
                      <td className="pr-2">Kırlentler:</td>
                      <td>{formatPrice(priceBreakdown.cushionPrice)}</td>
                    </tr>
                    <tr>
                      <td className="pr-2">Ek Seçenekler:</td>
                      <td>{formatPrice(priceBreakdown.additionalOptionsPrice)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default FurnitureCustomizer;