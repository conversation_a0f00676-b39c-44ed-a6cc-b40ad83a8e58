export type User = {
  id: string
  username: string
  role: 'admin' | 'store'
  storeName?: string
  isActive: boolean
  // Basic contact info
  email?: string
}

export type Product = {
  id: string
  name: string
  description: string
  price: number
  image: string
}

export type Order = {
  id: string
  userId: string
  products: { productId: string; quantity: number }[]
  totalPrice: number
  status: 'pending' | 'approved' | 'in_transit' | 'delivered'
  createdAt: string
  estimatedDelivery: string
}

export type PriceFormData = {
  sofaType: {
    threeSeater: number
    armchair: number
  }
  fabric: Record<string, number>
  backPillow: {
    none: number
    filled: number
    foam: number
  }
  cushions: {
    none: number
    small: number
    medium: number
    large: number
  }
  armrest: Record<string, number>
  seat: Record<string, number>
  lowerFrame: Record<string, number>
  lowerFrameColors: Record<string, number>
  legs: Record<string, number>
}

export type StoreSetting = {
  id?: string
  storeId?: string
  companyName: string
  taxNumber?: string
  ownerName?: string
  ownerPhone?: string
  storeAddress?: string
  storePhone?: string
  managerName?: string
  managerPhone?: string
  shippingCompany?: string
  shippingPhone?: string
  profitMargin: number
}