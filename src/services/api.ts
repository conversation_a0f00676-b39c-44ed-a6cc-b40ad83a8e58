import axios, { AxiosError, AxiosResponse } from 'axios'
import { toast } from 'react-toastify'

export const api = axios.create({
  baseURL: 'https://api.kapsulmobilya.com.tr',
  //baseURL: 'http://localhost:5030/api',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: 10000, // 10 seconds timeout
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error: AxiosError) => {
    console.error('Request setup error:', error.message)
    toast.error('Error setting up request')
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    console.error('API Error:', error)

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const status = error.response.status
      const data = error.response.data as any

      // Check if response is in problem+json format
      const isProblemJson = error.response.headers['content-type']?.includes('application/problem+json')

      // Extract meaningful error message
      let errorMessage = 'An error occurred'
      if (isProblemJson && data) {
        errorMessage = data.title || data.detail || 'Server error'
      } else if (data && typeof data === 'object') {
        errorMessage = data.message || data.error || JSON.stringify(data)
      }

      switch (status) {
        case 400:
          toast.error(`Bad request: ${errorMessage}`)
          break
        case 401:
          // Redirect to login page or refresh token
          localStorage.removeItem('token')
          toast.error('Unauthorized: Please log in again')
          window.location.href = '/auth'
          break
        case 403:
          toast.error(`Forbidden: ${errorMessage}`)
          break
        case 404:
          toast.error(`Not found: ${errorMessage}`)
          break
        case 422:
          toast.error(`Validation error: ${errorMessage}`)
          break
        case 500:
        case 502:
        case 503:
        case 504:
          toast.error(`Server error (${status}): ${errorMessage}`)
          break
        default:
          toast.error(`Error (${status}): ${errorMessage}`)
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request)
      toast.error('Network error: No response from server')
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Request setup error:', error.message)
      toast.error(`Request error: ${error.message}`)
    }

    return Promise.reject(error)
  }
)

// Helper to extract data from responses
export const extractData = <T>(response: AxiosResponse): T => {
  return response.data
}

// Part price interface
export interface PartPrice {
  id: string;
  name: string;
  price: number;
}

// Furniture component prices
export interface FurniturePrices {
  frames: PartPrice[];
  fabrics: PartPrice[];
  legs: PartPrice[];
  seats: PartPrice[];
  arms: PartPrice[];
  armOptions: PartPrice[];
  woodColors: PartPrice[];
  pillows: PartPrice[];
}

// --- Backend DTO typings (partial) ---
export interface PriceAndFabricDto {
  price: number;
  fabricAmount: number;
}

export interface PricingDataDto {
  id?: string | null;
  generalOutcomeArmchair: number;
  generalOutcomeBergere: number;
  profitRate: number;
  fixedCost: number;
  seatWithNoEar: Record<string, PriceAndFabricDto>;
  seatWithEar: Record<string, PriceAndFabricDto>;
  seatOptions: Record<string, PriceAndFabricDto>;
  seatSubOptions: Record<string, PriceAndFabricDto>;
  arms: Record<string, PriceAndFabricDto>;
  armExtensions: Record<string, PriceAndFabricDto>;
  armrestBase: Record<string, PriceAndFabricDto>;
  armrestPapel: Record<string, PriceAndFabricDto>;
  armrestKlapa: Record<string, PriceAndFabricDto>;
  legOptions: Record<string, number>;
  legColors: Record<string, number>;
  lowerFrame: Record<string, number>;
  lowerFrameColors: Record<string, number>;
  fabrics: Record<string, Record<string, number>>;
  backPillows: Record<string, PriceAndFabricDto>;
  cushions: Record<string, PriceAndFabricDto>;
}

export interface CartItemPayload {
  design: {
    legs: string;
    baseFrame: string;
    woodVeneer: string;
    armrest: {
      main: string;
      sub: string;
      mainAhsap: boolean;
    };
    seat: {
      option: string;
      sub: string;
    };
    sharedFabric: {
      option: string;
      color: string;
    };
    woodVeneerFabric: {
      option: string;
      color: string;
    };
    legFabric: {
      option: string;
      color: string;
    };
    mainAhsapFabric: {
      option: string;
      color: string;
    };
    model: string;
    armchairType?: string;
    backPillow?: string;
    cushions?: any;
    customerName: string;
    customerSurname: string;
    totalPrice: number;
    barcode: string;
  };
  quantity: number;
}

// Common API request methods
export const apiService = {
  get: async <T>(url: string, params = {}) => {
    const response = await api.get<T>(url, { params })
    return extractData<T>(response)
  },
  post: async <T>(url: string, data = {}) => {
    const response = await api.post<T>(url, data)
    return extractData<T>(response)
  },
  put: async <T>(url: string, data = {}) => {
    const response = await api.put<T>(url, data)
    return extractData<T>(response)
  },
  delete: async <T>(url: string) => {
    const response = await api.delete<T>(url)
    return extractData<T>(response)
  },

  // Furniture specific endpoints
  getAllPartPrices: async (): Promise<FurniturePrices> => {
    const response = await api.get('/furniture/pricing')
    return extractData<FurniturePrices>(response)
  }
}

// Extend apiService with new endpoints for unified backend
export const backendService = {
  ...apiService,

  getPricingData: async (): Promise<PricingDataDto> => {
    const response = await api.get('/furniture/pricing');
    return extractData<PricingDataDto>(response);
  },

  addToCart: async (payload: CartItemPayload) => {
    const response = await api.post('/orders/cart', payload);
    return extractData<any>(response);
  },

  addCustomDesignToCart: async (design: CartItemPayload['design']) => {
    const payload: CartItemPayload = {
      design,
      quantity: 1
    };
    const response = await api.post('/orders/cart', payload);
    return extractData<any>(response);
  },

  saveDesign: async (design: any) => {
    const response = await api.post('/orders/saved-designs', design);
    return extractData<any>(response);
  },

  getSavedDesigns: async () => {
    const response = await api.get('/orders/saved-designs');
    return extractData<any>(response);
  },

  getSavedDesignById: async (id: string) => {
    const response = await api.get(`/orders/saved-designs/${id}`);
    return extractData<any>(response);
  },

  deleteSavedDesign: async (id: string) => {
    const response = await api.delete(`/orders/saved-designs/${id}`);
    return extractData<any>(response);
  }
};

// Custom methods
export const apiGet = <T>(url: string, config = {}) => api.get<T>(url, config)
export const apiPost = <T>(url: string, data = {}, config = {}) => api.post<T>(url, data, config)
export const apiPut = <T>(url: string, data = {}, config = {}) => api.put<T>(url, data, config)
export const apiDelete = <T>(url: string, config = {}) => api.delete<T>(url, config)

// Admin Dashboard
export const getDashboardStats = async () => {
  const response = await api.get('/orders/dashboard-stats')
  return extractData<any>(response)
}
