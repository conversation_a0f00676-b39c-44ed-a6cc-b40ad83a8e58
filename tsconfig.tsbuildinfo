{"root": ["./src/app.tsx", "./src/i18n.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/admin/priceform.tsx", "./src/components/admin/settings.tsx", "./src/components/admin/singlepartmodel.tsx", "./src/components/admin/userlist.tsx", "./src/components/auth/authform.tsx", "./src/components/auth/login.tsx", "./src/components/common/button.tsx", "./src/components/common/card.tsx", "./src/components/common/checkbox.tsx", "./src/components/common/dialog.tsx", "./src/components/common/errorboundary.tsx", "./src/components/common/input.tsx", "./src/components/common/loading.tsx", "./src/components/common/select.tsx", "./src/components/common/tabs.tsx", "./src/components/layout/adminheader.tsx", "./src/components/layout/adminlayout.tsx", "./src/components/layout/adminsidebar.tsx", "./src/components/layout/header.tsx", "./src/components/layout/storesidebar.tsx", "./src/components/models/armchairmodel.tsx", "./src/components/models/bergeremodel.tsx", "./src/components/store/armchairmodel.tsx", "./src/components/store/babylonscene.tsx", "./src/components/store/bergeremodel.tsx", "./src/components/store/designtool.tsx", "./src/components/store/ordertracker.tsx", "./src/components/store/partupload.tsx", "./src/components/store/shoppingcart.tsx", "./src/components/store/singlepartmodel.tsx", "./src/hooks/useauth.ts", "./src/hooks/usecart.ts", "./src/pages/landingpage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/admin/admindashboard.tsx", "./src/pages/admin/adminsettings.tsx", "./src/pages/admin/costmanagement.tsx", "./src/pages/admin/dashboard.tsx", "./src/pages/admin/partupload.tsx", "./src/pages/admin/texturemanagement.tsx", "./src/pages/admin/usercreation.tsx", "./src/pages/admin/userlist.tsx", "./src/pages/admin/usermanagement.tsx", "./src/pages/auth/authform.tsx", "./src/pages/auth/login.tsx", "./src/pages/common/notfound.tsx", "./src/pages/store/about.tsx", "./src/pages/store/checkout.tsx", "./src/pages/store/customize.tsx", "./src/pages/store/furniturecustomizer.tsx", "./src/pages/store/orderconfirmation.tsx", "./src/pages/store/ordertracking.tsx", "./src/pages/store/productdetails.tsx", "./src/pages/store/products.tsx", "./src/pages/store/saveddesigns.tsx", "./src/pages/store/settings.tsx", "./src/pages/store/simpleglbloader.tsx", "./src/pages/store/storedesign.tsx", "./src/services/api.ts", "./src/types/index.ts", "./src/utils/helpers.ts"], "version": "5.8.3"}