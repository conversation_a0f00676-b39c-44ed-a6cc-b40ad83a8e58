using backend.DTOs.Users;
using backend.Models;
using backend.Repositories;
using backend.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Controllers
{
  [ApiController]
  [Route("api/[controller]")]
  public class UserController : ControllerBase
  {
    private readonly IRepository<User> _userRepository;
    private readonly IUserService _userService;

    public UserController(IRepository<User> userRepository, IUserService userService)
    {
      _userRepository = userRepository;
      _userService = userService;
    }

    // GET: api/User
    [Authorize(Roles = "admin")]
    [HttpGet]
    public async Task<ActionResult<IEnumerable<User>>> GetUsers()
    {
      var users = await _userRepository.GetAllAsync();
      return Ok(users);
    }

    // GET: api/User/{id}
    [Authorize(Roles = "admin")]
    [HttpGet("{id}")]
    public async Task<ActionResult<User>> GetUser(string id)
    {
      var user = await _userRepository.GetByIdAsync(id);
      if (user == null)
      {
        return NotFound();
      }
      return Ok(user);
    }

    // POST: api/User
    [Authorize(Roles = "admin")]
    [HttpPost]
    public async Task<ActionResult<User>> CreateUser(UserCreateDto user)
    {
      var newUser = new User
      {
        Username = user.Username,
        PasswordHash = BCrypt.Net.BCrypt.HashPassword(user.Password),
        StoreName = user.StoreName,
        Role = "store"
      };
      await _userRepository.CreateAsync(newUser);
      return CreatedAtAction(nameof(GetUser), new { id = newUser.Id }, newUser);
    }

    // PUT: api/User/{id}
    [Authorize(Roles = "admin")]
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateUser(string id, UserUpdateDto userUpdateDto)
    {
      var existingUser = await _userRepository.GetByIdAsync(id);
      if (existingUser == null)
      {
        return NotFound();
      }

      // Update all fields from DTO
      existingUser.Username = userUpdateDto.Username;
      existingUser.StoreName = userUpdateDto.StoreName;
      existingUser.IsActive = userUpdateDto.IsActive;
      existingUser.Role = userUpdateDto.Role;
      existingUser.Email = userUpdateDto.Email;

      await _userRepository.UpdateAsync(id, existingUser);
      return NoContent();
    }

    // DELETE: api/User/{id}
    [Authorize(Roles = "admin")]
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser(string id)
    {
      var user = await _userRepository.GetByIdAsync(id);
      if (user == null)
      {
        return NotFound();
      }

      await _userRepository.DeleteAsync(id);
      return NoContent();
    }
  }
}
